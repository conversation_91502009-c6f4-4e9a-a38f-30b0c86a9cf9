import cv2
import numpy as np
from paddleocr import PaddleOCR
import platform
from utils.image_processor import preprocess_image_mask_green_bubbles, filter_text_by_color
import time
import os
import re

class MessageExtractor:
    def __init__(self, ocr_params=None):
        """
        初始化消息提取器
        :param ocr_params: OCR参数字典
        """
        # 默认OCR参数
        default_params = {
            'use_angle_cls': True,     # 使用角度分类器
            'lang': 'ch',              # 中文识别
            'use_gpu': False,          # 不使用GPU
            'show_log': False,         # 不显示日志
            'det_db_thresh': 0.2,      # 降低检测阈值，增加检测区域
            'det_db_box_thresh': 0.3,  # 降低框阈值
            'det_db_unclip_ratio': 1.8, # 增加unclip比例，扩大检测区域
            'det_model_dir': './model/det',
            'cls_model_dir': './model/cls',
            'rec_model_dir': './model/rec'
        }
        
        # Windows平台特殊处理
        if platform.system() == 'Windows':
            # 在Windows上禁用某些可能导致问题的功能
            default_params['use_angle_cls'] = False  # Windows上角度分类器可能有问题
            default_params['enable_mkldnn'] = False  # 禁用mkldnn加速
            default_params['use_tensorrt'] = False   # 禁用tensorrt
            default_params['det_db_thresh'] = 0.3    # 提高检测阈值，减少错误检测
            default_params['det_db_box_thresh'] = 0.5  # 提高框阈值，减少错误检测
            default_params['det_db_unclip_ratio'] = 1.6  # 减小unclip比例，减少错误检测
            # 确保使用默认字典路径
            if 'rec_char_dict_path' not in default_params:
                default_params['rec_char_dict_path'] = 'ppocr/utils/ppocr_keys_v1.txt'
        
        # 用户参数覆盖默认参数
        if ocr_params:
            default_params.update(ocr_params)
            
        # 初始化PaddleOCR
        try:
            self.ocr = PaddleOCR(**default_params)
            self.ocr_initialized = True
            print("PaddleOCR初始化成功")
        except Exception as e:
            print(f"初始化PaddleOCR失败: {str(e)}")
            # 尝试使用更保守的参数重新初始化
            try:
                print("尝试使用备用参数初始化PaddleOCR...")
                # 更保守的参数
                backup_params = {
                    'use_angle_cls': False,     # 禁用角度分类器
                    'lang': 'ch',              # 中文识别
                    'use_gpu': False,          # 不使用GPU
                    'show_log': False,         # 不显示日志
                    'enable_mkldnn': False,    # 禁用mkldnn加速
                    'use_tensorrt': False,     # 禁用tensorrt
                    'det_db_thresh': 0.3,      # 提高检测阈值
                    'det_db_box_thresh': 0.5,  # 提高框阈值
                }
                self.ocr = PaddleOCR(**backup_params)
                self.ocr_initialized = True
                print("PaddleOCR使用备用参数初始化成功")
            except Exception as e2:
                print(f"使用备用参数初始化PaddleOCR也失败: {str(e2)}")
                self.ocr = None
                self.ocr_initialized = False
        
    def extract_messages(self, image, params):
        """
        从图像中提取消息
        :param image: OpenCV格式的图像
        :param params: 参数字典，包含各种设置参数
        :return: 消息列表, 联系人名称
        """
        try:
            # 获取参数
            debug_mode = params.get('debug_mode', False)
            mask_green_bubbles = params.get('mask_green_bubbles', True)
            text_color_enabled = params.get('text_color_enabled', False)
            current_contact = params.get('current_contact', None)
            screen_center_x = params.get('screen_center_x', 0)
            x1 = params.get('x1', 0)
            y1 = params.get('y1', 0)
            y2 = params.get('y2', 0)
            center_margin = params.get('center_margin', 50)
            line_spacing_threshold = params.get('line_spacing_threshold', 15)
            conversation_history = params.get('conversation_history', [])
            
            # 检查OCR是否正确初始化
            if not self.ocr_initialized or self.ocr is None:
                if debug_mode:
                    print("OCR未正确初始化，无法识别文本")
                return [], current_contact
            
            # 初始化遮罩中心坐标
            mask_center_x = None
            mask_center_y = None
            
            # 预处理: 如果启用了绿色气泡遮罩，覆盖绿色气泡(助手回复)区域
            if mask_green_bubbles:
                if debug_mode:
                    print("应用绿色气泡遮罩预处理...")
                # 获取绿色气泡参数
                green_params = {
                    'green_r': params.get('green_r', 80),
                    'green_g': params.get('green_g', 200),
                    'green_b': params.get('green_b', 80),
                    'green_tol': params.get('green_tol', 40),
                    'green_min_area': params.get('green_min_area', 100),
                    'debug_mode': debug_mode
                }
                # 覆盖绿色气泡，使OCR无法识别这些区域的文字
                # 同时获取最后一个遮罩的中心坐标
                image, mask_center_x, mask_center_y = preprocess_image_mask_green_bubbles(image, green_params)
                if debug_mode:
                    print("绿色气泡遮罩处理完成")
                    if mask_center_x is not None and mask_center_y is not None:
                        print(f"获取到最后一个遮罩中心坐标: ({mask_center_x}, {mask_center_y})")
                    else:
                        print("未获取到遮罩中心坐标")
            
            # 文字颜色过滤: 如果启用了文字颜色过滤，只保留特定颜色的文字
            if text_color_enabled:
                if debug_mode:
                    print("应用文字颜色过滤...")
                # 获取文字颜色参数
                color_params = {
                    'text_color_r': params.get('text_color_r', 255),  # 默认白色
                    'text_color_g': params.get('text_color_g', 255),
                    'text_color_b': params.get('text_color_b', 255),
                    'text_color_tol': params.get('text_color_tol', 40),
                    'debug_mode': debug_mode
                }
                # 过滤图像，只保留指定颜色的文字
                image = filter_text_by_color(image, color_params)
                if debug_mode:
                    print("文字颜色过滤处理完成")
            
            # 保存原始图像用于调试(Windows平台)
            if debug_mode and platform.system() == 'Windows':
                debug_dir = "debug_screenshots"
                os.makedirs(debug_dir, exist_ok=True)
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                debug_path = os.path.join(debug_dir, f"ocr_input_{timestamp}.png")
                cv2.imwrite(debug_path, image, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                print(f"已保存OCR输入图像: {debug_path}")
            
            # 使用PaddleOCR识别文本
            try:
                result = self.ocr.ocr(image, cls=True)
            except RuntimeError as e:
                if debug_mode:
                    print(f"PaddleOCR运行时错误: {str(e)}")
                    print("尝试使用不同的参数重新运行OCR...")
                
                # 尝试使用备用设置再次运行OCR
                try:
                    # 创建一个新的OCR对象，禁用可能导致问题的功能
                    backup_params = {
                        'use_angle_cls': False,   # 禁用角度分类器
                        'lang': 'ch',             # 中文识别
                        'use_gpu': False,         # 不使用GPU
                        'show_log': False,        # 不显示日志
                        'enable_mkldnn': False,   # 禁用mkldnn
                        'det_db_thresh': 0.3,     # 降低检测阈值，增加检测区域  
                    }
                    
                    backup_ocr = PaddleOCR(**backup_params)
                    result = backup_ocr.ocr(image, cls=False)  # 禁用angle_cls
                    if debug_mode:
                        print("备用OCR设置成功运行")
                except Exception as e2:
                    if debug_mode:
                        print(f"备用OCR也失败: {str(e2)}")
                    result = None
            except Exception as e:
                if debug_mode:
                    print(f"PaddleOCR识别出错: {str(e)}")
                result = None
            
            messages = []
            contact_name = None
            
            # 如果没有识别到任何文本，提早返回
            if not result or len(result) == 0:
                if debug_mode:
                    print("OCR未识别到任何文本")
                return messages, contact_name
            
            # 使用current_contact作为联系人名称，不再从OCR结果中提取
            contact_name = current_contact
            
            # 处理消息文本
            # 存储所有可能的消息
            all_possible_messages = []
            
            # 遍历OCR识别结果，提取消息
            for line in result:
                if isinstance(line, list) and len(line) > 0:
                    for item in line:
                        try:
                            # OCR结果格式: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], [text, confidence]]
                            if len(item) >= 2 and item[1] is not None and len(item[1]) >= 2:
                                text = item[1][0]
                                confidence = item[1][1]
                                
                                # 跳过空文本或置信度过低的文本
                                if not text or confidence < 0.6:
                                    if debug_mode and text:
                                        print(f"  跳过低置信度文本: '{text}' (置信度: {confidence:.2f})")
                                    continue
                                
                                # 获取文本框的位置坐标
                                coords = item[0]  # 四个角的坐标
                                
                                # 计算文本框的平均横坐标和纵坐标
                                avg_x = sum(coord[0] for coord in coords) / 4
                                avg_y = sum(coord[1] for coord in coords) / 4
                                
                                # 定义中心区域边界
                                center_x = screen_center_x - x1  # 调整为相对于截图的坐标
                                center_left = center_x - center_margin
                                center_right = center_x + center_margin
                                
                                # 不立即过滤中心区域的消息，而是标记它们
                                is_center = center_left <= avg_x <= center_right
                                if is_center and debug_mode:
                                    print(f"  标记中心区域文本: '{text}' (位置: {avg_x})")
                                
                                # 根据横坐标判断角色
                                # 左侧是用户消息，右侧是AI回复
                                print(f"{text}:avg_x: {avg_x}, center_x: {center_x}")
                                
                                # 直接根据位置进行判断，不依赖于avg_x和center_x的比较
                                # 对于avg_x，检查它是在图像的左半部分还是右半部分
                                image_width = image.shape[1]  # 获取图像宽度
                                is_left_side = avg_x < (image_width / 2)
                                print(f"{text}:image_width: {image_width}, avg_x: {avg_x}, is_left_side: {is_left_side}")
                                
                                # 基于图像位置判断角色
                                role = "user" if is_left_side else "assistant"
                                
                                # 判断消息是否已回复
                                is_replied = False
                                
                                # 根据遮罩中心坐标判断消息是否已回复
                                # 如果遮罩中心坐标存在，且消息在遮罩中心坐标之上，则视为已回复消息
                                # if mask_center_x is not None and mask_center_y is not None:
                                #     # 如果消息位置在最后一个遮罩的中心Y坐标上方，则标记为已回复
                                #     if avg_y < mask_center_y:
                                #         is_replied = True
                                #         if debug_mode:
                                #             print(f"  消息'{text}'位于遮罩中心坐标({mask_center_x}, {mask_center_y})上方，标记为已回复")
                                
                                # 创建消息对象，包含中心区域标记和是否已回复标记
                                message = {
                                    "role": role,
                                    "content": text.strip().replace(" ",""),
                                    "confidence": confidence,
                                    "position": {"x": avg_x, "y": avg_y},
                                    "is_center": is_center,  # 标记是否在中心区域
                                    "is_replied": is_replied,  # 设置是否已回复标记
                                    "timestamp": int(time.time())  # 添加时间戳，记录消息创建时间
                                }
                                
                                if debug_mode:
                                    reply_status = "已回复" if is_replied else "未回复"
                                    print(f"  检测到文本: '{text}' (置信度: {confidence:.2f}, 位置: ({avg_x}, {avg_y}), 角色: {role}, 状态: {reply_status})")
                                all_possible_messages.append(message)
                        except Exception as e:
                            if debug_mode:
                                print(f"处理OCR结果项时出错: {str(e)}")
            
            # 按照y坐标排序所有消息，从上到下
            all_possible_messages.sort(key=lambda m: m["position"]["y"])
            
            # 根据消息内容和位置进行分组
            # 初始化为空消息列表
            messages = []
            
            # 跳过中心区域的消息
            filtered_messages = all_possible_messages
            # for msg in all_possible_messages:
            #     if not msg.get("is_center", False):
            #         filtered_messages.append(msg)
            #     elif debug_mode:
            #         print(f"  过滤中心区域文本: '{msg['content']}' (位置: {msg['position']['x']}, {msg['position']['y']})")
            
            # 最后，基于纵坐标的接近程度，对消息进行分组，识别同一条消息的多个部分
            # 同一条消息的多行文本通常在纵向上连续排列
            if filtered_messages:
                current_group = [filtered_messages[0]]
                last_y = filtered_messages[0]["position"]["y"]
                
                for i in range(1, len(filtered_messages)):
                    msg = filtered_messages[i]
                    curr_y = msg["position"]["y"]
                    y_gap = curr_y - last_y
                    
                    # 如果行间距小于阈值，认为是同一条消息的一部分
                    if y_gap <= line_spacing_threshold:
                        current_group.append(msg)
                    else:
                        # 行间距大于阈值，处理前一组并开始新组
                        if len(current_group) > 1:
                            # 检查是否所有消息都在中心区域
                            all_in_center = all(m.get("is_center", False) for m in current_group)
                            
                            if all_in_center:
                                # 整个组的所有行都在中心区域，可以过滤
                                if debug_mode:
                                    print(f"  过滤整组中心区域文本: {[m['content'] for m in current_group]}")
                            else:
                                # 按照行顺序拼接同一组消息
                                current_group.sort(key=lambda m: m["position"]["y"])
                                combined_content = " ".join(m["content"] for m in current_group)
                                
                                # 使用第一行消息的角色和位置信息
                                first_msg = current_group[0]
                                # 如果组内任一消息被标记为已回复，则整个消息都视为已回复
                                is_replied = any(m.get("is_replied", False) for m in current_group)
                                
                                combined_msg = {
                                    "role": first_msg["role"],
                                    "content": combined_content,
                                    "position": first_msg["position"],
                                    "is_replied": is_replied,
                                    "timestamp": first_msg.get("timestamp", int(time.time()))  # 保留第一条消息的时间戳，如果没有则创建
                                }
                                
                                messages.append(combined_msg)
                                if debug_mode:
                                    reply_status = "已回复" if is_replied else "未回复"
                                    print(f"  合并组消息: '{combined_content}' (角色: {first_msg['role']}, 状态: {reply_status})")
                        else:
                            messages.append(current_group[0])
                            # 只有一条消息，检查是否在中心区域
                            # if not current_group[0].get("is_center", False):
                            #     messages.append(current_group[0])
                            # elif debug_mode:
                            #     print(f"  过滤单条中心区域文本: {current_group[0]['content']}")
                        
                        # 开始新组
                        current_group = [msg]
                    
                    last_y = curr_y
                
                # 处理最后一组消息
                if current_group:
                    if len(current_group) > 1:
                        # 检查是否所有消息都在中心区域
                        all_in_center = all(m.get("is_center", False) for m in current_group)
                        
                        if all_in_center:
                            # 整个组的所有行都在中心区域，可以过滤
                            if debug_mode:
                                print(f"  过滤整组中心区域文本: {[m['content'] for m in current_group]}")
                        else:
                            # 按照行顺序拼接同一组消息
                            current_group.sort(key=lambda m: m["position"]["y"])
                            combined_content = " ".join(m["content"] for m in current_group)
                            
                            # 使用第一行消息的角色和位置信息
                            first_msg = current_group[0]
                            # 如果组内任一消息被标记为已回复，则整个消息都视为已回复
                            is_replied = any(m.get("is_replied", False) for m in current_group)
                            
                            combined_msg = {
                                "role": first_msg["role"],
                                "content": combined_content.replace(" ",""),
                                "position": first_msg["position"],
                                "is_replied": is_replied,
                                "timestamp": first_msg.get("timestamp", int(time.time()))  # 保留第一条消息的时间戳，如果没有则创建
                            }
                            
                            messages.append(combined_msg)
                            if debug_mode:
                                reply_status = "已回复" if is_replied else "未回复"
                                print(f"  合并组最后一组消息: '{combined_content}' (角色: {first_msg['role']}, 状态: {reply_status})")
                    else:
                        messages.append(current_group[0])
                        # 只有一条消息，检查是否在中心区域
                        # if not current_group[0].get("is_center", False):
                        #     messages.append(current_group[0])
                        # elif debug_mode:
                        #     print(f"  过滤单条中心区域文本: {current_group[0]['content']}")
            
            # 根据纵坐标重新排序消息，确保从上到下的顺序
            messages.sort(key=lambda m: m["position"]["y"])
            
            if debug_mode:
                replied_count = sum(1 for m in messages if m.get("is_replied", False))
                print(f"最终提取到{len(messages)}条消息，其中{replied_count}条已回复，联系人: {contact_name}")
            
            return messages, contact_name
        except Exception as e:
            if debug_mode:
                print(f"消息提取处理失败: {str(e)}")
                import traceback
                print(traceback.format_exc())
            return [], None

    def extract_contact_name(self, image, params):
        """
        从联系人名称区域图像中提取联系人名称
        :param image: OpenCV格式的图像
        :param params: 参数字典，包含各种设置参数
        :return: 联系人名称字符串，如果未识别出则返回None
        """
        try:
            debug_mode = params.get('debug_mode', False)
            
            # 检查OCR是否正确初始化
            if not self.ocr_initialized or self.ocr is None:
                if debug_mode:
                    print("OCR未正确初始化，无法识别联系人名称")
                return None
            
            # 保存原始图像用于调试
            if debug_mode:
                debug_dir = "debug_screenshots"
                os.makedirs(debug_dir, exist_ok=True)
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                debug_path = os.path.join(debug_dir, f"contact_ocr_input_{timestamp}.png")
                cv2.imwrite(debug_path, image, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                print(f"已保存联系人OCR输入图像: {debug_path}")
            
            # Windows平台特殊处理
            is_windows = platform.system() == 'Windows'
            
            # 图像预处理，提高OCR成功率
            if is_windows:
                # 对图像进行预处理，增强文字对比度
                # 转换为灰度图
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                # 自适应阈值处理，增强文字与背景的对比度
                binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
                # 轻微模糊，减少噪点
                processed_img = cv2.GaussianBlur(binary, (3, 3), 0)
                
                if debug_mode:
                    debug_processed_path = os.path.join(debug_dir, f"contact_processed_{timestamp}.png")
                    cv2.imwrite(debug_processed_path, processed_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                    print(f"已保存预处理后的联系人图像: {debug_processed_path}")
                
                # 在Windows上，直接使用更保守的OCR参数
                try:
                    backup_params = {
                        'use_angle_cls': False,   # 禁用角度分类器
                        'lang': 'ch',             # 中文识别
                        'use_gpu': False,         # 不使用GPU
                        'show_log': False,        # 不显示日志
                        'enable_mkldnn': False,   # 禁用mkldnn
                        'det_db_thresh': 0.3,     # 检测阈值
                        # 'rec_char_dict_path': 'ppocr/utils/ppocr_keys_v1.txt',  # 使用默认字典路径
                    }
                    
                    backup_ocr = PaddleOCR(**backup_params)
                    # 先尝试处理后的图像
                    result = backup_ocr.ocr(processed_img, cls=False)
                    
                    # 如果处理后的图像没有识别结果，尝试原始图像
                    if not result or len(result) == 0:
                        if debug_mode:
                            print("预处理图像OCR未识别到文本，尝试原始图像")
                        result = backup_ocr.ocr(image, cls=False)
                    
                    if debug_mode:
                        print("Windows平台OCR设置成功运行")
                except Exception as e2:
                    if debug_mode:
                        print(f"Windows平台OCR失败: {str(e2)}")
                    result = None
            else:
                # 非Windows平台使用标准OCR流程
                try:
                    result = self.ocr.ocr(image, cls=True)
                except RuntimeError as e:
                    if debug_mode:
                        print(f"联系人OCR运行时错误: {str(e)}")
                        print("尝试使用不同的参数重新运行OCR...")
                    
                    # 尝试使用备用设置再次运行OCR
                    try:
                        backup_params = {
                            'use_angle_cls': False,   # 禁用角度分类器
                            'lang': 'ch',             # 中文识别
                            'use_gpu': False,         # 不使用GPU
                            'show_log': False,        # 不显示日志
                            'enable_mkldnn': False,   # 禁用mkldnn
                            'det_db_thresh': 0.3,     # 降低检测阈值
                        }
                        
                        backup_ocr = PaddleOCR(**backup_params)
                        result = backup_ocr.ocr(image, cls=False)  # 禁用angle_cls
                        if debug_mode:
                            print("备用联系人OCR设置成功运行")
                    except Exception as e2:
                        if debug_mode:
                            print(f"备用联系人OCR也失败: {str(e2)}")
                        result = None
                except Exception as e:
                    if debug_mode:
                        print(f"联系人OCR识别出错: {str(e)}")
                    result = None
            
            # 如果没有识别到任何文本，提早返回
            if not result or len(result) == 0:
                if debug_mode:
                    print("联系人名称OCR未识别到任何文本")
                return None
            
            contact_name = None
            str_contact_name = ""
            
            # 遍历OCR识别结果，提取文本
            for line in result:
                if isinstance(line, list) and len(line) > 0:
                    for item in line:
                        try:
                            # OCR结果格式: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], [text, confidence]]
                            if len(item) >= 2 and item[1] is not None and len(item[1]) >= 2:
                                text = item[1][0].strip()
                                str_contact_name += text
                        except Exception as e:
                            if debug_mode:
                                print(f"处理联系人名称OCR结果项时出错: {str(e)}")
            
            # 返回最终的联系人名称
            if str_contact_name:
                if debug_mode:
                    print(f"最终识别出的联系人名称: '{str_contact_name}'")
                return str_contact_name.replace(" ","")
            else:
                if debug_mode:
                    print("无法提取有效的联系人名称")
                return None
                
        except Exception as e:
            if debug_mode:
                print(f"联系人名称提取处理失败: {str(e)}")
                import traceback
                print(traceback.format_exc())
            return None 
        
    def extract_wxid(self, image, params):
        """
        从唯一标识区域图像中提取wxid
        :param image: OpenCV格式的图像
        :param params: 参数字典，包含各种设置参数
        :return: wxid字符串，如果未识别出则返回None
        """
        try:
            debug_mode = params.get('debug_mode', False)
            
            # 检查OCR是否正确初始化
            if not self.ocr_initialized or self.ocr is None:
                if debug_mode:
                    print("OCR未正确初始化，无法识别wxid")
                return None
            
            # 保存原始图像用于调试
            if debug_mode:
                debug_dir = "debug_screenshots"
                os.makedirs(debug_dir, exist_ok=True)
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                debug_path = os.path.join(debug_dir, f"wxid_ocr_input_{timestamp}.png")
                cv2.imwrite(debug_path, image, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                print(f"已保存wxid OCR输入图像: {debug_path}")
            
            # Windows平台特殊处理
            is_windows = platform.system() == 'Windows'
            
            # 图像预处理，提高OCR成功率
            if is_windows:
                # 对图像进行预处理，增强文字对比度
                # 转换为灰度图
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                # 自适应阈值处理，增强文字与背景的对比度
                binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
                # 轻微模糊，减少噪点
                processed_img = cv2.GaussianBlur(binary, (3, 3), 0)
                
                if debug_mode:
                    debug_processed_path = os.path.join(debug_dir, f"wxid_processed_{timestamp}.png")
                    cv2.imwrite(debug_processed_path, processed_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                    print(f"已保存预处理后的wxid图像: {debug_processed_path}")
                
                # 在Windows上，直接使用更保守的OCR参数
                try:
                    backup_params = {
                        'use_angle_cls': False,   # 禁用角度分类器
                        'lang': 'ch',             # 中文识别
                        'use_gpu': False,         # 不使用GPU
                        'show_log': False,        # 不显示日志
                        'enable_mkldnn': False,   # 禁用mkldnn
                        'det_db_thresh': 0.3,     # 检测阈值
                        # 'rec_char_dict_path': 'ppocr/utils/ppocr_keys_v1.txt',  # 使用默认字典路径
                    }
                    
                    backup_ocr = PaddleOCR(**backup_params)
                    # 先尝试处理后的图像
                    result = backup_ocr.ocr(processed_img, cls=False)
                    
                    # 如果处理后的图像没有识别结果，尝试原始图像
                    if not result or len(result) == 0:
                        if debug_mode:
                            print("预处理图像OCR未识别到文本，尝试原始图像")
                        result = backup_ocr.ocr(image, cls=False)
                    
                    if debug_mode:
                        print("Windows平台OCR设置成功运行")
                except Exception as e2:
                    if debug_mode:
                        print(f"Windows平台OCR失败: {str(e2)}")
                    result = None
            else:
                # 非Windows平台使用标准OCR流程
                try:
                    result = self.ocr.ocr(image, cls=True)
                except RuntimeError as e:
                    if debug_mode:
                        print(f"wxid OCR运行时错误: {str(e)}")
                        print("尝试使用不同的参数重新运行OCR...")
                    
                    # 尝试使用备用设置再次运行OCR
                    try:
                        backup_params = {
                            'use_angle_cls': False,   # 禁用角度分类器
                            'lang': 'ch',             # 中文识别
                            'use_gpu': False,         # 不使用GPU
                            'show_log': False,        # 不显示日志
                            'enable_mkldnn': False,   # 禁用mkldnn
                            'det_db_thresh': 0.3,     # 降低检测阈值
                        }
                        
                        backup_ocr = PaddleOCR(**backup_params)
                        result = backup_ocr.ocr(image, cls=False)  # 禁用angle_cls
                        if debug_mode:
                            print("备用wxid OCR设置成功运行")
                    except Exception as e2:
                        if debug_mode:
                            print(f"备用wxid OCR也失败: {str(e2)}")
                        result = None
                except Exception as e:
                    if debug_mode:
                        print(f"wxid OCR识别出错: {str(e)}")
                    result = None
            
            # 如果没有识别到任何文本，提早返回
            if not result or len(result) == 0:
                if debug_mode:
                    print("wxid OCR未识别到任何文本")
                return None
            
            contact_name = None
            str_contact_name = ""
            
            # 遍历OCR识别结果，提取文本
            for line in result:
                if isinstance(line, list) and len(line) > 0:
                    for item in line:
                        try:
                            # OCR结果格式: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], [text, confidence]]
                            if len(item) >= 2 and item[1] is not None and len(item[1]) >= 2:
                                text = item[1][0].strip()
                                str_contact_name += text
                        except Exception as e:
                            if debug_mode:
                                print(f"处理wxid OCR结果项时出错: {str(e)}")
            
            # 返回最终的联系人名称
            if str_contact_name:
                if debug_mode:
                    print(f"最终识别出的wxid: '{str_contact_name}'")
                return str_contact_name.replace(" ","")
            else:
                if debug_mode:
                    print("无法提取有效的wxid")
                return None
                
        except Exception as e:
            if debug_mode:
                print(f"wxid提取处理失败: {str(e)}")
                import traceback
                print(traceback.format_exc())
            return None 