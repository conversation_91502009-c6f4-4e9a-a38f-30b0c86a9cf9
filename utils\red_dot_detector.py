import cv2
import numpy as np
import os
import time

def find_red_dot_opencv(image_path, lower_red=(220, 80, 80), upper_red=(255, 120, 120), min_radius=3, debug_mode=False):
    """
    原始函数 - 查找红色圆点
    """
    import cv2
    import numpy as np
    try:
        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            return []
            
        # 转换为HSV颜色空间，这更利于检测特定颜色
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 红色在HSV中分为两个范围
        # 第一个范围：色相接近0的红色
        lower_red_1 = np.array([0, 100, 100])
        upper_red_1 = np.array([10, 255, 255])
        # 第二个范围：色相接近180的红色
        lower_red_2 = np.array([160, 100, 100])
        upper_red_2 = np.array([180, 255, 255])
        
        # 创建掩码
        mask1 = cv2.inRange(hsv, lower_red_1, upper_red_1)
        mask2 = cv2.inRange(hsv, lower_red_2, upper_red_2)
        mask = cv2.bitwise_or(mask1, mask2)
        
        # 保存掩码用于调试（当debug_mode打开时）
        if debug_mode:
            # 保存掩码图像用于调试
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            debug_path = os.path.join(debug_dir, f"red_mask_{timestamp}.png")
            cv2.imwrite(debug_path, mask, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            print(f"已保存红点掩码: {debug_path}")
        
        # 应用高斯模糊，减少噪点
        blurred = cv2.GaussianBlur(mask, (5, 5), 0)
        
        # 使用Hough圆检测方法找圆形
        circles = cv2.HoughCircles(blurred, cv2.HOUGH_GRADIENT, dp=1, minDist=20,
                                param1=50, param2=30, minRadius=min_radius, maxRadius=50)
        
        found_dots = []
        if circles is not None:
            circles = np.uint16(np.around(circles))
            for i in circles[0, :]:
                center_x, center_y = i[0], i[1]
                found_dots.append((int(center_x), int(center_y)))
        
        return found_dots
    except Exception as e:
        print(f"Error in find_red_dot_opencv: {str(e)}")
        return []

def find_red_area(image_path, lower_red=(220, 80, 80), upper_red=(255, 120, 120), debug_mode=False):
    """
    新函数 - 查找任何红色区域，不限制形状和大小
    """
    try:
        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            return []
            
        # 直接在BGR空间进行颜色过滤
        # BGR格式，注意 lower_red 和 upper_red 已经是BGR格式的 (B,G,R)
        lower_bgr = np.array([lower_red[0], lower_red[1], lower_red[2]], dtype=np.uint8)
        upper_bgr = np.array([upper_red[0], upper_red[1], upper_red[2]], dtype=np.uint8)
        
        # 直接在BGR空间创建掩码
        mask = cv2.inRange(img, lower_bgr, upper_bgr)
        
        if debug_mode:
            # 打印调试信息
            print(f"检测颜色范围 - BGR下限: {lower_bgr}, BGR上限: {upper_bgr}")
            print(f"掩码中非零像素数: {np.count_nonzero(mask)}")
            
            # 保存掩码和原图用于调试
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            
            # 保存掩码
            debug_path = os.path.join(debug_dir, f"red_mask_{timestamp}.png")
            cv2.imwrite(debug_path, mask, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            
            # 保存带轮廓的原图
            debug_img = img.copy()
            debug_img_path = os.path.join(debug_dir, f"debug_img_{timestamp}.png")
            cv2.imwrite(debug_img_path, debug_img)
            
            print(f"已保存调试图像: {debug_path}, {debug_img_path}")
        
        # 查找连通区域
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        found_areas = []
        for contour in contours:
            # 只考虑一定面积以上的轮廓，过滤噪点
            area = cv2.contourArea(contour)
            if area < 5:  # 可以根据需要调整最小面积
                continue
                
            # 计算轮廓的重心
            M = cv2.moments(contour)
            if M["m00"] > 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                found_areas.append((cx, cy))
                
                if debug_mode:
                    print(f"找到红色区域: 位置({cx}, {cy}), 面积: {area}")
        
        return found_areas
    except Exception as e:
        print(f"Error in find_red_area: {str(e)}")
        return []

def get_rea_xy(image_file_path, lower_red_bgr=(80,80,220), upper_red_bgr=(120,120,255), debug_mode=False):
    """
    原始函数 - 使用圆形检测方法
    """
    min_red_radius = 5
    new_message_dots = find_red_dot_opencv(image_file_path, lower_red_bgr, upper_red_bgr, min_red_radius, debug_mode)
    if new_message_dots:
        for x, y in new_message_dots:
            return x, y
    else:
        return None, None

def get_red_area_xy(image_file_path, lower_red_bgr=(80,80,220), upper_red_bgr=(120,120,255), debug_mode=False):
    """
    新函数 - 使用任何红色区域检测方法
    """
    red_areas = find_red_area(image_file_path, lower_red_bgr, upper_red_bgr, debug_mode)
    if red_areas:
        for x, y in red_areas:
            return x, y
    else:
        return None, None

# 将主函数替换为新的检测方法
get_rea_xy = get_red_area_xy 