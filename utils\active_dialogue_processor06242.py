import os
import json
import time
import threading
import pyautogui
import pyperclip
from datetime import datetime
import platform

class ActiveDialogueProcessor06242:
    """
    主动对话处理器，负责管理主动对话队列和执行对话操作
    """
    def __init__(self, app=None):
        """
        初始化主动对话处理器
        :param app: 应用程序实例
        """
        self.app = app
        self.dialogue_queue06242 = []  # 对话队列
        self.is_processing06242 = False  # 是否正在处理队列
        self.progress_event06242 = None  # 进度控制事件
        self.progress_callback06242 = None  # 进度回调函数
        
    def add_to_queue06242(self, contact_name, dialogue_content):
        """
        添加对话到队列
        :param contact_name: 联系人名称
        :param dialogue_content: 对话内容
        """
        self.dialogue_queue06242.append({
            'name': contact_name,
            'content': dialogue_content,
            'timestamp': time.time()
        })
        
    def clear_queue06242(self):
        """清空对话队列"""
        self.dialogue_queue06242 = []
        
    def start_processing06242(self, search_area, progress_callback=None):
        """
        开始处理对话队列
        :param search_area: 搜索区域坐标
        :param progress_callback: 进度回调函数(current_index, total, status, contact_name)
        """
        if self.is_processing06242:
            return False
            
        if not self.dialogue_queue06242:
            return False
            
        if not search_area:
            return False
            
        # 设置进度回调
        self.progress_callback06242 = progress_callback
        
        # 创建进度控制事件
        self.progress_event06242 = threading.Event()
        
        # 启动处理线程
        self.processing_thread06242 = threading.Thread(
            target=self._process_queue06242,
            args=(search_area,),
            daemon=True
        )
        self.is_processing06242 = True
        self.processing_thread06242.start()
        
        return True
        
    def stop_processing06242(self):
        """停止处理队列"""
        if self.progress_event06242:
            self.progress_event06242.set()
            
    def _process_queue06242(self, search_area):
        """
        处理对话队列的内部方法
        :param search_area: 搜索区域坐标
        """
        try:
            # 计算搜索区域中心点
            search_center_x = (search_area[0] + search_area[2]) // 2
            search_center_y = (search_area[1] + search_area[3]) // 2
            
            # 保存原始的pyautogui暂停设置
            original_pause = pyautogui.PAUSE
            # 设置操作间隔
            pyautogui.PAUSE = 0.1
            
            total = len(self.dialogue_queue06242)
            
            for idx, dialogue in enumerate(self.dialogue_queue06242):
                # 检查是否被取消
                if self.progress_event06242.is_set():
                    break
                    
                contact_name = dialogue['name']
                content = dialogue['content']
                
                # 更新进度
                if self.progress_callback06242:
                    self.progress_callback06242(idx, total, "正在处理", contact_name)
                
                # 点击搜索区域中心
                pyautogui.click(search_center_x, search_center_y)
                time.sleep(0.5)
                pyautogui.click(search_center_x, search_center_y)
                time.sleep(0.5)
                
                # 粘贴联系人名称
                pyperclip.copy(contact_name)
                pyautogui.hotkey('command', 'v') if platform.system() == "Darwin" else pyautogui.hotkey('ctrl', 'v')
                time.sleep(0.5)
                
                # 按回车
                pyautogui.press('enter')
                time.sleep(0.5)
                
                # 粘贴对话内容
                pyperclip.copy(content)
                pyautogui.hotkey('command', 'v') if platform.system() == "Darwin" else pyautogui.hotkey('ctrl', 'v')
                time.sleep(0.5)
                
                # 按回车发送消息
                pyautogui.press('enter')
                time.sleep(0.5)
                
                # 更新进度
                if self.progress_callback06242:
                    self.progress_callback06242(idx + 1, total, "已完成", contact_name)
            
            # 全部处理完成
            if self.progress_callback06242:
                self.progress_callback06242(total, total, "全部完成", "")
                
        except Exception as e:
            import traceback
            print(f"处理对话队列时出错: {str(e)}")
            print(traceback.format_exc())
            
            # 报告错误
            if self.progress_callback06242:
                self.progress_callback06242(-1, total, f"错误: {str(e)}", "")
                
        finally:
            # 恢复原始设置
            pyautogui.PAUSE = original_pause
            self.is_processing06242 = False
            
    def save_conversation06242(self, contact_name, content):
        """
        保存对话记录
        :param contact_name: 联系人名称
        :param content: 对话内容
        """
        try:
            # 确保chat_history文件夹存在
            if not os.path.exists("chat_history"):
                os.makedirs("chat_history")
                
            # 构建文件路径
            file_path = os.path.join("chat_history", f"{contact_name}.json")
            
            # 读取现有对话记录
            conversations = []
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        conversations = json.load(f)
                except:
                    conversations = []
            
            # 添加新的对话记录
            new_message = {
                "role": "assistant",
                "content": content,
                "timestamp": time.time(),
                "time_str": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "is_active_dialogue": True  # 标记为主动对话
            }
            conversations.append(new_message)
            
            # 保存对话记录
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(conversations, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            import traceback
            print(f"保存对话记录时出错: {str(e)}")
            print(traceback.format_exc())
            return False 