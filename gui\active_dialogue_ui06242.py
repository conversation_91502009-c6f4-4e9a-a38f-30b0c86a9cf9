import tkinter as tk
from tkinter import ttk, messagebox
import os
import json
import time
from datetime import datetime
import threading
import pyautogui
import pyperclip
from utils.active_dialogue_processor06242 import ActiveDialogueProcessor06242
import requests

class ActiveDialogueUI06242:
    def __init__(self, parent, callbacks):
        """
        初始化主动对话UI
        :param parent: 父窗口
        :param callbacks: 回调函数字典
        """
        self.parent = parent
        self.callbacks = callbacks
        
        # 表格数据
        self.contacts_data06242 = []  # 存储联系人数据
        self.contact_queue06242 = []  # 存储待处理的联系人队列
        
        # 创建主动对话处理器
        self.processor06242 = ActiveDialogueProcessor06242()
        
        # 创建主框架
        self.main_frame06242 = ttk.Frame(parent)
        self.main_frame06242.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建UI元素
        self.create_ui_elements()
        
    def create_ui_elements(self):
        """创建UI元素"""
        # 添加标题
        ttk.Label(self.main_frame06242, text="主动对话设置", font=('微软雅黑', 11, 'bold')).pack(anchor=tk.W, padx=5, pady=5)
        
        # 顶部按钮框架
        buttons_frame06242 = ttk.Frame(self.main_frame06242)
        buttons_frame06242.pack(fill=tk.X, padx=5, pady=5)
        
        # 添加自动加载按钮
        load_btn06242 = ttk.Button(
            buttons_frame06242,
            text="加载",
            command=self.load_contact_history
        )
        load_btn06242.pack(side=tk.LEFT, padx=5)
        
        # 添加自动生成主动对话按钮
        generate_btn06242 = ttk.Button(
            buttons_frame06242,
            text="自动生成主动对话",
            command=self.auto_generate_dialogue
        )
        generate_btn06242.pack(side=tk.LEFT, padx=5)
        
        # 添加主动对话生成提示词按钮
        prompt_btn06242 = ttk.Button(
            buttons_frame06242,
            text="主动对话生成提示词",
            command=self.edit_generation_prompt
        )
        prompt_btn06242.pack(side=tk.LEFT, padx=5)
        
        # 中央区域表单：联系人列表
        contact_frame06242 = ttk.LabelFrame(self.main_frame06242, text="联系人列表")
        contact_frame06242.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建一个包含树形列表和滚动条的框架
        list_container06242 = ttk.Frame(contact_frame06242)
        list_container06242.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建Treeview显示联系人列表
        self.contacts_tree06242 = ttk.Treeview(
            list_container06242, 
            columns=("勾选", "联系人", "最后聊天时间", "聊天总结", "总结时间", "主动对话内容"), 
            show="headings", 
            height=10
        )
        self.contacts_tree06242.heading("勾选", text="勾选")
        self.contacts_tree06242.heading("联系人", text="联系人")
        self.contacts_tree06242.heading("最后聊天时间", text="最后聊天时间")
        self.contacts_tree06242.heading("聊天总结", text="聊天总结")
        self.contacts_tree06242.heading("总结时间", text="总结时间")
        self.contacts_tree06242.heading("主动对话内容", text="主动对话内容")
        
        # 调整列宽
        self.contacts_tree06242.column("勾选", width=50, anchor=tk.CENTER)
        self.contacts_tree06242.column("联系人", width=100)
        self.contacts_tree06242.column("最后聊天时间", width=150)
        self.contacts_tree06242.column("聊天总结", width=300)
        self.contacts_tree06242.column("总结时间", width=150)
        self.contacts_tree06242.column("主动对话内容", width=300)
        
        self.contacts_tree06242.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar06242 = ttk.Scrollbar(list_container06242, orient="vertical", command=self.contacts_tree06242.yview)
        scrollbar06242.pack(side=tk.RIGHT, fill=tk.Y)
        self.contacts_tree06242.configure(yscrollcommand=scrollbar06242.set)
        
        # 绑定双击事件以便编辑主动对话内容
        self.contacts_tree06242.bind("<Double-1>", self.edit_dialogue_content)
        
        # 添加/删除联系人框架
        add_delete_frame06242 = ttk.LabelFrame(self.main_frame06242, text="添加/删除联系人")
        add_delete_frame06242.pack(fill=tk.X, padx=5, pady=5)
        
        # 添加联系人输入框和按钮
        add_frame06242 = ttk.Frame(add_delete_frame06242)
        add_frame06242.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(add_frame06242, text="联系人名称:").pack(side=tk.LEFT, padx=5)
        self.contact_name_var06242 = tk.StringVar()
        contact_entry06242 = ttk.Entry(add_frame06242, textvariable=self.contact_name_var06242, width=20)
        contact_entry06242.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(add_frame06242, text="主动对话内容:").pack(side=tk.LEFT, padx=5)
        self.dialogue_content_var06242 = tk.StringVar()
        dialogue_entry06242 = ttk.Entry(add_frame06242, textvariable=self.dialogue_content_var06242, width=30)
        dialogue_entry06242.pack(side=tk.LEFT, padx=5)
        
        # 添加按钮
        add_btn06242 = ttk.Button(
            add_frame06242,
            text="添加",
            command=self.add_contact
        )
        add_btn06242.pack(side=tk.LEFT, padx=5)
        
        # 删除按钮
        delete_btn06242 = ttk.Button(
            add_frame06242,
            text="删除",
            command=self.delete_selected_contact
        )
        delete_btn06242.pack(side=tk.LEFT, padx=5)
        
        # 开始主动对话按钮
        start_frame06242 = ttk.Frame(self.main_frame06242)
        start_frame06242.pack(fill=tk.X, padx=5, pady=10)
        
        start_btn06242 = ttk.Button(
            start_frame06242, 
            text="开始主动对话", 
            command=self.start_active_dialogue
        )
        start_btn06242.pack(side=tk.RIGHT, padx=5)
        
        # 创建全选按钮和其勾选状态
        self.select_all_var = tk.BooleanVar()
        select_all_checkbox = ttk.Checkbutton(
            buttons_frame06242,
            text="全选",
            variable=self.select_all_var,
            command=self.toggle_select_all
        )
        select_all_checkbox.pack(side=tk.LEFT, padx=5)
        
        # 绑定点击事件以切换勾选状态
        self.contacts_tree06242.bind("<ButtonRelease-1>", self.toggle_checkbox)
    
    def load_contact_history(self):
        """加载联系人历史聊天数据"""
        try:
            # 清空表格数据
            for item in self.contacts_tree06242.get_children():
                self.contacts_tree06242.delete(item)
            
            self.contacts_data06242 = []
            
            # 加载聊天记录
            from utils.storage import StorageManager
            storage = StorageManager()
            conversations, _ = storage.load_all_conversations()
            
            # 加载聊天总结数据
            summary_data = {}
            if os.path.exists("summary_chat"):
                for filename in os.listdir("summary_chat"):
                    if filename.endswith(".json"):
                        contact_name = os.path.splitext(filename)[0]
                        file_path = os.path.join("summary_chat", filename)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                contact_summaries = json.load(f)
                                if contact_summaries:
                                    latest_summary = contact_summaries[-1]
                                    summary_data[contact_name] = {
                                        "summary": latest_summary.get("summary_content", ""),
                                        "timestamp": latest_summary.get("last_time_summary", 0)
                                    }
                        except Exception as e:
                            print(f"读取总结文件 {file_path} 出错: {str(e)}")
            
            # 处理每个联系人的数据
            for contact_name, conversation in conversations.items():
                if not conversation:
                    continue
                
                # 获取最后一条用户消息
                last_user_message = None
                last_message_time = 0
                for msg in reversed(conversation):
                    if msg.get("role") == "user":
                        last_user_message = msg.get("content", "")
                        last_message_time = msg.get("timestamp", 0)
                        break
                
                if not last_user_message:
                    continue
                
                # 格式化最后消息时间
                last_time_str = datetime.fromtimestamp(last_message_time).strftime("%Y-%m-%d %H:%M:%S") if last_message_time else ""
                
                # 获取最新的聊天总结
                summary = ""
                summary_time_str = ""
                if contact_name in summary_data:
                    summary = summary_data[contact_name]["summary"]
                    summary_timestamp = summary_data[contact_name]["timestamp"]
                    summary_time_str = datetime.fromtimestamp(summary_timestamp).strftime("%Y-%m-%d %H:%M:%S") if summary_timestamp else ""
                
                # 自动生成一个简单的主动对话内容
                dialogue_content = f"你好，{contact_name}，最近怎么样？"
                
                # 添加到数据列表
                contact_data = {
                    "name": contact_name,
                    "last_message_time": last_message_time,
                    "last_message_time_str": last_time_str,
                    "summary": summary,
                    "summary_time_str": summary_time_str,
                    "dialogue_content": dialogue_content,
                    "checked": False  # 添加勾选状态
                }
                self.contacts_data06242.append(contact_data)
                
                # 添加到表格，第一列为空字符串表示未勾选
                self.contacts_tree06242.insert("", tk.END, values=(
                    "",  # 勾选列，初始为空
                    contact_name,
                    last_time_str,
                    summary[:50] + "..." if len(summary) > 50 else summary,
                    summary_time_str,
                    dialogue_content
                ))
            
            messagebox.showinfo("加载完成", f"已加载 {len(self.contacts_data06242)} 个联系人的聊天历史。")
            
        except Exception as e:
            print(f"加载联系人历史聊天数据时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            messagebox.showerror("错误", f"加载联系人历史聊天数据时出错: {str(e)}")
    
    def auto_generate_dialogue(self):
        """自动生成主动对话内容"""
        # 检查是否有勾选的联系人
        selected_contacts = []
        for item_id in self.contacts_tree06242.get_children():
            values = self.contacts_tree06242.item(item_id, "values")
            if values[0] == "√":  # 勾选的联系人
                contact_data = {
                    "name": values[1],
                    "summary": values[3],
                    "dialogue_content": values[5]
                }
                selected_contacts.append(contact_data)
        
        if not selected_contacts:
            messagebox.showinfo("提示", "请先勾选需要生成主动对话的联系人")
            return
            
        # 加载提示词模板
        prompt_template = self.load_generation_prompt()
        if not prompt_template:
            prompt_template = "你是一个能够生成个性化主动对话的助手。请根据以下信息为我生成一条自然、友好且有针对性的主动对话消息：\n\n联系人: {contact_name}\n最新聊天总结: {summary}\n最近聊天记录: {chat_history}\n\n请生成一条合适的主动对话消息，长度控制在100字以内，语气自然友好，内容要与对方最近的聊天相关。"
        
        # 创建进度窗口
        progress_window = self.create_generation_progress_window(selected_contacts)
        
        # 启动生成线程
        threading.Thread(
            target=self.generate_dialogue_thread,
            args=(selected_contacts, prompt_template, progress_window),
            daemon=True
        ).start()
    
    def create_generation_progress_window(self, contacts):
        """创建生成进度窗口"""
        progress_window = tk.Toplevel(self.parent)
        progress_window.title("生成主动对话进度")
        progress_window.geometry("400x300")
        progress_window.transient(self.parent)
        progress_window.grab_set()
        progress_window.resizable(True, True)
        
        # 确保对话框在父窗口中央
        progress_window.update_idletasks()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        dialog_width = progress_window.winfo_width()
        dialog_height = progress_window.winfo_height()
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        progress_window.geometry(f"+{x}+{y}")
        
        # 进度标签
        progress_label = ttk.Label(
            progress_window,
            text="正在生成主动对话...",
            font=('微软雅黑', 10)
        )
        progress_label.pack(padx=20, pady=10)
        
        # 进度条
        progress_bar = ttk.Progressbar(
            progress_window,
            orient=tk.HORIZONTAL,
            length=350,
            mode='determinate'
        )
        progress_bar.pack(padx=20, pady=10, fill=tk.X)
        
        # 当前处理的联系人
        current_contact_label = ttk.Label(
            progress_window,
            text="",
            font=('微软雅黑', 10)
        )
        current_contact_label.pack(padx=20, pady=5)
        
        # 状态信息
        status_label = ttk.Label(
            progress_window,
            text="准备中...",
            font=('微软雅黑', 9)
        )
        status_label.pack(padx=20, pady=5)
        
        # 消息日志框
        log_frame = ttk.LabelFrame(progress_window, text="生成日志")
        log_frame.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)
        
        # 创建文本框和滚动条
        scrollbar = ttk.Scrollbar(log_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        log_text = tk.Text(
            log_frame,
            height=8,
            width=40,
            wrap=tk.WORD,
            state=tk.DISABLED,
            font=('微软雅黑', 9),
            yscrollcommand=scrollbar.set
        )
        log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.config(command=log_text.yview)
        
        # 取消按钮
        cancel_btn = ttk.Button(
            progress_window,
            text="取消",
            command=lambda: progress_window.destroy()
        )
        cancel_btn.pack(pady=10)
        
        # 保存窗口部件的引用
        progress_window.progress_bar = progress_bar
        progress_window.progress_label = progress_label
        progress_window.current_contact_label = current_contact_label
        progress_window.status_label = status_label
        progress_window.log_text = log_text
        progress_window.cancel_btn = cancel_btn
        
        # 设置窗口关闭协议
        def on_close():
            progress_window.destroy()
        progress_window.protocol("WM_DELETE_WINDOW", on_close)
        
        return progress_window
    
    def generate_dialogue_thread(self, contacts, prompt_template, progress_window):
        """生成主动对话的线程函数"""
        try:
            total = len(contacts)
            for i, contact in enumerate(contacts):
                # 检查窗口是否已关闭
                if not self.is_window_alive(progress_window):
                    return
                
                # 更新进度
                progress = (i / total) * 100
                self.update_progress_window(
                    progress_window,
                    progress,
                    f"正在为 {contact['name']} 生成主动对话 ({i+1}/{total})",
                    contact['name'],
                    f"正在处理..."
                )
                
                # 加载联系人的聊天历史
                chat_history = self.load_chat_history(contact['name'])
                
                # 替换提示词中的变量
                prompt = prompt_template.replace("{contact_name}", contact['name'])
                prompt = prompt.replace("{summary}", contact['summary'])
                prompt = prompt.replace("{chat_history}", chat_history)
                
                # 调用OpenAI API生成对话内容
                self.update_progress_window(
                    progress_window,
                    progress,
                    f"正在为 {contact['name']} 生成主动对话 ({i+1}/{total})",
                    contact['name'],
                    f"正在调用AI生成..."
                )
                
                dialogue_content = self.call_openai_api(prompt)
                
                # 更新表格和数据
                self.update_contact_dialogue(contact['name'], dialogue_content)
                
                # 记录日志
                self.log_to_progress_window(
                    progress_window,
                    f"已为 {contact['name']} 生成对话: {dialogue_content[:50]}..."
                )
            
            # 完成所有生成
            self.update_progress_window(
                progress_window,
                100,
                f"已完成所有联系人的主动对话生成",
                "",
                f"已完成! 共处理 {total} 个联系人"
            )
            
        except Exception as e:
            import traceback
            error_msg = str(e)
            stacktrace = traceback.format_exc()
            print(f"生成主动对话时出错: {error_msg}")
            print(stacktrace)
            
            if self.is_window_alive(progress_window):
                self.update_progress_window(
                    progress_window,
                    0,
                    "生成主动对话出错",
                    "",
                    f"错误: {error_msg}"
                )
                self.log_to_progress_window(progress_window, f"错误详情: {stacktrace[:200]}...")
    
    def is_window_alive(self, window):
        """检查窗口是否仍然存在"""
        try:
            return window.winfo_exists()
        except:
            return False
    
    def update_progress_window(self, window, progress, progress_text, contact_name, status_text):
        """更新进度窗口"""
        if not self.is_window_alive(window):
            return
            
        try:
            window.progress_bar['value'] = progress
            window.progress_label.config(text=progress_text)
            window.current_contact_label.config(text=f"当前联系人: {contact_name}")
            window.status_label.config(text=status_text)
            window.update()
        except:
            pass
    
    def log_to_progress_window(self, window, message):
        """在进度窗口的日志区域添加消息"""
        if not self.is_window_alive(window):
            return
            
        try:
            window.log_text.config(state=tk.NORMAL)
            window.log_text.insert(tk.END, message + "\n")
            window.log_text.see(tk.END)
            window.log_text.config(state=tk.DISABLED)
            window.update()
        except:
            pass
    
    def load_chat_history(self, contact_name):
        """加载联系人的聊天历史"""
        try:
            chat_file_path = os.path.join("chat_history", f"{contact_name}.json")
            if not os.path.exists(chat_file_path):
                return "无聊天历史记录"
                
            with open(chat_file_path, 'r', encoding='utf-8') as f:
                chat_data = json.load(f)
                
            # 只保留最近的10条记录
            recent_chats = chat_data[-10:] if len(chat_data) > 10 else chat_data
            
            # 格式化聊天记录
            history_text = ""
            for msg in recent_chats:
                role = "用户" if msg.get("role") == "user" else "助手"
                content = msg.get("content", "")
                time_str = msg.get("time_str", "")
                history_text += f"{role} ({time_str}): {content}\n\n"
                
            return history_text if history_text else "无聊天历史记录"
            
        except Exception as e:
            print(f"加载聊天历史出错: {str(e)}")
            return "加载聊天历史出错"
    
    def call_openai_api(self, prompt):
        """调用OpenAI API生成对话内容"""
        try:
            # 从settings.json加载API设置
            settings = {}
            if os.path.exists('settings.json'):
                with open('settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
            
            api_key = settings.get('api_key', '')
            api_base_url = settings.get('api_base_url', 'https://api.openai.com/v1')
            model_name = settings.get('model_name', 'gpt-3.5-turbo')
            provider = settings.get('provider', 'openai')
            
            if not api_key:
                return "未配置API密钥，请在API设置中配置"
            
            
            # 准备请求头和数据
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }
            
            payload = {
                "model": model_name,
                "messages": [
                    {"role": "system", "content": "你是一个能够生成个性化主动对话的助手。请生成一条自然、友好且有针对性的主动对话消息。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.7,
                "max_tokens": 300
            }
            
            # 根据不同供应商调整API端点
            endpoint = f"{api_base_url}/chat/completions"
            
            # 发送请求
            response = requests.post(endpoint, headers=headers, json=payload)
            response_data = response.json()
            
            # 解析响应
            if "choices" in response_data and len(response_data["choices"]) > 0:
                message = response_data["choices"][0]["message"]["content"].strip()
                return message
            else:
                error_msg = response_data.get("error", {}).get("message", "Unknown error")
                print(f"API调用错误: {error_msg}")
                return f"生成失败: {error_msg}"
                
        except Exception as e:
            print(f"调用API时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return f"生成失败: {str(e)}"
    
    def update_contact_dialogue(self, contact_name, dialogue_content):
        """更新联系人的主动对话内容"""
        # 更新表格中的数据
        for item_id in self.contacts_tree06242.get_children():
            values = self.contacts_tree06242.item(item_id, "values")
            if values[1] == contact_name:
                new_values = list(values)
                new_values[5] = dialogue_content
                self.contacts_tree06242.item(item_id, values=tuple(new_values))
                break
                
        # 更新数据列表
        for contact in self.contacts_data06242:
            if contact["name"] == contact_name:
                contact["dialogue_content"] = dialogue_content
                break
    
    def edit_generation_prompt(self):
        """编辑主动对话生成提示词"""
        # 创建对话框窗口
        prompt_dialog = tk.Toplevel(self.parent)
        prompt_dialog.title("编辑主动对话生成提示词")
        prompt_dialog.geometry("600x400")
        prompt_dialog.transient(self.parent)
        prompt_dialog.grab_set()
        prompt_dialog.resizable(True, True)
        
        # 确保对话框在父窗口中央
        prompt_dialog.update_idletasks()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        dialog_width = prompt_dialog.winfo_width()
        dialog_height = prompt_dialog.winfo_height()
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        prompt_dialog.geometry(f"+{x}+{y}")
        
        # 加载当前提示词
        current_prompt = self.load_generation_prompt()
        
        # 顶部标签
        ttk.Label(
            prompt_dialog,
            text="请输入主动对话生成提示词",
            font=('微软雅黑', 11, 'bold')
        ).pack(anchor=tk.W, padx=10, pady=10)
        
        # 提示词说明
        ttk.Label(
            prompt_dialog,
            text="提示词将用于指导AI生成更合适的主动对话内容，可以使用以下变量：",
            font=('微软雅黑', 10)
        ).pack(anchor=tk.W, padx=10)
        
        # 变量说明
        variables_frame = ttk.Frame(prompt_dialog)
        variables_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(
            variables_frame,
            text="{contact_name}: 联系人名称\n{summary}: 联系人的最新总结\n{chat_history}: 最近的聊天记录",
            font=('微软雅黑', 9),
            justify=tk.LEFT
        ).pack(anchor=tk.W)
        
        # 文本编辑区域
        text_frame = ttk.Frame(prompt_dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 文本框
        self.prompt_text = tk.Text(
            text_frame,
            wrap=tk.WORD,
            font=('微软雅黑', 10),
            yscrollcommand=scrollbar.set
        )
        self.prompt_text.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.prompt_text.yview)
        
        # 设置当前提示词
        if current_prompt:
            self.prompt_text.insert(tk.END, current_prompt)
        else:
            # 默认提示词
            default_prompt = "你是一个能够生成个性化主动对话的助手。请根据以下信息为我生成一条自然、友好且有针对性的主动对话消息：\n\n联系人: {contact_name}\n最新聊天总结: {summary}\n最近聊天记录: {chat_history}\n\n请生成一条合适的主动对话消息，长度控制在100字以内，语气自然友好，内容要与对方最近的聊天相关。"
            self.prompt_text.insert(tk.END, default_prompt)
        
        # 按钮区域
        btn_frame = ttk.Frame(prompt_dialog)
        btn_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 取消按钮
        cancel_btn = ttk.Button(
            btn_frame,
            text="取消",
            command=prompt_dialog.destroy
        )
        cancel_btn.pack(side=tk.RIGHT, padx=5)
        
        # 保存按钮
        save_btn = ttk.Button(
            btn_frame,
            text="保存",
            command=lambda: self.save_generation_prompt(self.prompt_text.get(1.0, tk.END).strip(), prompt_dialog)
        )
        save_btn.pack(side=tk.RIGHT, padx=5)
    
    def load_generation_prompt(self):
        """加载主动对话生成提示词"""
        try:
            # 从settings.json中加载提示词
            if os.path.exists('settings.json'):
                with open('settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    return settings.get('active_dialogue_prompt', '')
            return ''
        except Exception as e:
            print(f"加载主动对话生成提示词出错: {str(e)}")
            return ''
    
    def save_generation_prompt(self, prompt, dialog):
        """保存主动对话生成提示词"""
        if not prompt:
            messagebox.showerror("错误", "提示词不能为空")
            return
        
        try:
            # 读取当前设置
            settings = {}
            if os.path.exists('settings.json'):
                with open('settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
            
            # 更新设置
            settings['active_dialogue_prompt'] = prompt
            
            # 保存设置
            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("成功", "主动对话生成提示词已保存")
            dialog.destroy()
        
        except Exception as e:
            print(f"保存主动对话生成提示词出错: {str(e)}")
            messagebox.showerror("错误", f"保存提示词时出错: {str(e)}")
    
    def edit_dialogue_content(self, event):
        """编辑主动对话内容"""
        # 获取选中项
        selected_items = self.contacts_tree06242.selection()
        if not selected_items:
            return
            
        # 获取当前选中项的ID
        item_id = selected_items[0]
        
        # 获取当前值
        current_values = self.contacts_tree06242.item(item_id, "values")
        if not current_values or len(current_values) < 6:
            return
            
        # 获取联系人名称
        contact_name = current_values[1]
            
        # 从数据列表中获取完整的信息
        contact_data = None
        for contact in self.contacts_data06242:
            if contact["name"] == contact_name:
                contact_data = contact
                break
                
        # 如果未找到联系人数据，使用表格中的数据
        if not contact_data:
            contact_data = {
                "name": current_values[1],
                "last_message_time_str": current_values[2],
                "summary": current_values[3],
                "summary_time_str": current_values[4],
                "dialogue_content": current_values[5]
            }
            
        # 创建编辑对话框
        edit_dialog = tk.Toplevel(self.parent)
        edit_dialog.title("联系人详情")
        edit_dialog.geometry("500x320")
        edit_dialog.transient(self.parent)  # 设置为父窗口的临时窗口
        edit_dialog.grab_set()  # 模态窗口
        
        # 居中显示
        edit_dialog.update_idletasks()
        width = edit_dialog.winfo_width()
        height = edit_dialog.winfo_height()
        x = (edit_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (edit_dialog.winfo_screenheight() // 2) - (height // 2)
        edit_dialog.geometry(f"{width}x{height}+{x}+{y}")
        
        # 创建可滚动的内容框架
        main_frame = ttk.Frame(edit_dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 联系人信息
        ttk.Label(main_frame, text="联系人:", font=('微软雅黑', 10, 'bold')).grid(row=0, column=0, padx=10, pady=5, sticky=tk.W)
        ttk.Label(main_frame, text=contact_data["name"], font=('微软雅黑', 10)).grid(row=0, column=1, padx=10, pady=5, sticky=tk.W)
        
        # 最后聊天时间
        ttk.Label(main_frame, text="最后聊天时间:", font=('微软雅黑', 10, 'bold')).grid(row=1, column=0, padx=10, pady=5, sticky=tk.W)
        ttk.Label(main_frame, text=contact_data["last_message_time_str"], font=('微软雅黑', 10)).grid(row=1, column=1, padx=10, pady=5, sticky=tk.W)
        
        # 总结时间
        ttk.Label(main_frame, text="总结时间:", font=('微软雅黑', 10, 'bold')).grid(row=2, column=0, padx=10, pady=5, sticky=tk.W)
        ttk.Label(main_frame, text=contact_data["summary_time_str"], font=('微软雅黑', 10)).grid(row=2, column=1, padx=10, pady=5, sticky=tk.W)
        
        # 聊天总结
        ttk.Label(main_frame, text="聊天总结:", font=('微软雅黑', 10, 'bold')).grid(row=3, column=0, padx=10, pady=5, sticky=tk.NW)
        
        summary_frame = ttk.Frame(main_frame)
        summary_frame.grid(row=3, column=1, padx=10, pady=5, sticky=tk.NSEW)
        
        summary_scrollbar = ttk.Scrollbar(summary_frame)
        summary_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        summary_text = tk.Text(summary_frame, height=5, width=40, wrap=tk.WORD, font=('微软雅黑', 10))
        summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_text.insert(tk.END, contact_data["summary"])
        summary_text.config(state=tk.DISABLED)  # 设为只读
        
        summary_scrollbar.config(command=summary_text.yview)
        summary_text.config(yscrollcommand=summary_scrollbar.set)
        
        # 主动对话内容
        ttk.Label(main_frame, text="主动对话内容:", font=('微软雅黑', 10, 'bold')).grid(row=4, column=0, padx=10, pady=10, sticky=tk.NW)
        
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=4, column=1, padx=10, pady=10, sticky=tk.NSEW)
        
        content_scrollbar = ttk.Scrollbar(content_frame)
        content_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        content_text = tk.Text(content_frame, height=5, width=40, wrap=tk.WORD, font=('微软雅黑', 10))
        content_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        content_text.insert(tk.END, contact_data["dialogue_content"])
        
        content_scrollbar.config(command=content_text.yview)
        content_text.config(yscrollcommand=content_scrollbar.set)
        
        # 添加按钮
        button_frame = ttk.Frame(edit_dialog)
        button_frame.pack(fill=tk.X, pady=10)
        
        def save_changes():
            # 获取文本框的内容
            new_dialogue_content = content_text.get(1.0, tk.END).strip()
            
            # 更新表格中的数据
            new_values = list(current_values)
            new_values[5] = new_dialogue_content
            self.contacts_tree06242.item(item_id, values=new_values)
            
            # 更新数据列表中的对应项
            for contact in self.contacts_data06242:
                if contact["name"] == contact_name:
                    contact["dialogue_content"] = new_dialogue_content
                    break
            
            edit_dialog.destroy()
        
        ttk.Button(button_frame, text="保存", command=save_changes).pack(side=tk.RIGHT, padx=10)
        ttk.Button(button_frame, text="取消", command=edit_dialog.destroy).pack(side=tk.RIGHT, padx=10)
        
        # 设置焦点到内容输入框
        content_text.focus_set()
    
    def add_contact(self):
        """添加联系人到列表"""
        contact_name = self.contact_name_var06242.get().strip()
        dialogue_content = self.dialogue_content_var06242.get().strip()
        
        if not contact_name:
            messagebox.showerror("错误", "请输入联系人名称")
            return
            
        if not dialogue_content:
            messagebox.showerror("错误", "请输入主动对话内容")
            return
            
        # 检查是否已存在
        for contact in self.contacts_data06242:
            if contact["name"] == contact_name:
                messagebox.showerror("错误", f"联系人 '{contact_name}' 已存在")
                return
        
        # 创建新联系人数据
        new_contact = {
            "name": contact_name,
            "last_message_time": 0,
            "last_message_time_str": "",
            "summary": "",
            "summary_time_str": "",
            "dialogue_content": dialogue_content,
            "checked": False
        }
        
        # 添加到数据列表
        self.contacts_data06242.append(new_contact)
        
        # 添加到表格
        self.contacts_tree06242.insert("", tk.END, values=(
            "",
            contact_name,
            "",
            "",
            "",
            dialogue_content
        ))
        
        # 清空输入框
        self.contact_name_var06242.set("")
        self.dialogue_content_var06242.set("")
    
    def delete_selected_contact(self):
        """删除选中的联系人"""
        selected_items = self.contacts_tree06242.selection()
        if not selected_items:
            messagebox.showinfo("提示", "请先选择要删除的联系人")
            return
            
        # 确认删除
        if not messagebox.askyesno("确认", "确定要删除选中的联系人吗？"):
            return
            
        # 获取选中项的联系人名称
        item_id = selected_items[0]
        contact_name = self.contacts_tree06242.item(item_id, "values")[1]
        
        # 从表格中删除
        self.contacts_tree06242.delete(item_id)
        
        # 从数据列表中删除
        self.contacts_data06242 = [c for c in self.contacts_data06242 if c["name"] != contact_name]
    
    def start_active_dialogue(self):
        """开始主动对话"""
        # 清空队列
        self.processor06242.clear_queue06242()
        
        # 获取所有勾选的联系人
        checked_contacts = []
        for item_id in self.contacts_tree06242.get_children():
            values = self.contacts_tree06242.item(item_id, "values")
            if values[0] == "√":  # 使用勾选的联系人
                checked_contacts.append({
                    "name": values[1],
                    "dialogue_content": values[5]
                })
        
        # 如果没有勾选的联系人，提示用户
        if not checked_contacts:
            messagebox.showinfo("提示", "请先勾选需要发送主动对话的联系人")
            return
        
        # 添加勾选的联系人到队列
        for contact in checked_contacts:
            if contact["dialogue_content"].strip():
                self.processor06242.add_to_queue06242(contact["name"], contact["dialogue_content"])
        
        # 检查队列是否为空
        if not self.processor06242.dialogue_queue06242:
            messagebox.showinfo("提示", "没有可执行的主动对话")
            return
        
        # 确认对话数量
        if not messagebox.askyesno("确认", f"即将开始为 {len(self.processor06242.dialogue_queue06242)} 个联系人发送主动对话，是否继续？"):
            return
        
        # 获取搜索区域坐标
        search_area = None
        if self.callbacks.get("get_settings"):
            settings = self.callbacks["get_settings"]()
            if settings and "search_area" in settings:
                search_area = settings["search_area"]
        
        if not search_area:
            messagebox.showerror("错误", "未设置搜索选区，请先在区域设置中设置搜索选区")
            return
            
        # 显示进度窗口并开始处理
        self.show_progress_window(search_area)
    
    def show_progress_window(self, search_area):
        """显示进度窗口"""
        progress_window = tk.Toplevel(self.parent)
        progress_window.title("主动对话进度")
        progress_window.geometry("400x150")
        progress_window.transient(self.parent)  # 设置为父窗口的临时窗口
        
        # 居中显示
        progress_window.update_idletasks()
        width = progress_window.winfo_width()
        height = progress_window.winfo_height()
        x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
        y = (progress_window.winfo_screenheight() // 2) - (height // 2)
        progress_window.geometry(f"{width}x{height}+{x}+{y}")
        
        # 添加标签和进度条
        ttk.Label(progress_window, text="正在执行主动对话...").pack(padx=20, pady=10)
        
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_window, orient="horizontal", length=300, mode="determinate", variable=progress_var)
        progress_bar.pack(padx=20, pady=10)
        
        status_var = tk.StringVar(value="准备中...")
        status_label = ttk.Label(progress_window, textvariable=status_var)
        status_label.pack(padx=20, pady=5)
        
        # 添加取消按钮
        def cancel_dialogue():
            self.processor06242.stop_processing06242()
            status_var.set("正在取消...")
            
        button_frame = ttk.Frame(progress_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        cancel_btn = ttk.Button(button_frame, text="取消", command=cancel_dialogue)
        cancel_btn.pack(side=tk.RIGHT)
        
        # 进度回调函数
        def progress_callback(current, total, status, contact_name):
            if total > 0:
                progress_var.set((current / total) * 100)
                
            if contact_name:
                status_var.set(f"{status}: {contact_name}")
            else:
                status_var.set(status)
                
            # 检查是否完成
            if status.startswith("全部完成") or status.startswith("错误"):
                # 在短暂延迟后关闭窗口
                progress_window.after(1500, progress_window.destroy)
                
        # 启动处理
        self.processor06242.start_processing06242(search_area, progress_callback) 
    
    def toggle_select_all(self):
        """切换全选状态"""
        if self.select_all_var.get():
            for item_id in self.contacts_tree06242.get_children():
                current_values = self.contacts_tree06242.item(item_id, "values")
                if len(current_values) >= 6:  # 确保有足够的值
                    self.contacts_tree06242.item(item_id, values=("√", current_values[1], current_values[2], 
                                                               current_values[3], current_values[4], current_values[5]))
        else:
            for item_id in self.contacts_tree06242.get_children():
                current_values = self.contacts_tree06242.item(item_id, "values")
                if len(current_values) >= 6:  # 确保有足够的值
                    self.contacts_tree06242.item(item_id, values=("", current_values[1], current_values[2], 
                                                               current_values[3], current_values[4], current_values[5]))
    
    def toggle_checkbox(self, event):
        """切换勾选状态"""
        # 获取点击的列
        region = self.contacts_tree06242.identify("region", event.x, event.y)
        if region != "cell":
            return
            
        # 获取点击的单元格
        column = self.contacts_tree06242.identify_column(event.x)
        column_id = int(column.replace('#', ''))
        
        # 只有点击第一列时才切换复选框
        if column_id != 1:  # 第一列的ID是1
            return
            
        # 获取点击的行
        row = self.contacts_tree06242.identify_row(event.y)
        if not row:
            return
            
        # 获取当前值
        current_values = self.contacts_tree06242.item(row, "values")
        if not current_values or len(current_values) < 6:
            return
            
        # 切换勾选状态
        check_status = "√" if current_values[0] != "√" else ""
        
        # 更新表格中的值
        self.contacts_tree06242.item(row, values=(check_status, current_values[1], current_values[2], 
                                               current_values[3], current_values[4], current_values[5]))
        
        # 检查是否所有项都被选中，更新全选按钮状态
        all_checked = True
        for item_id in self.contacts_tree06242.get_children():
            if self.contacts_tree06242.item(item_id, "values")[0] != "√":
                all_checked = False
                break
        self.select_all_var.set(all_checked) 