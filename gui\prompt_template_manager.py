import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import re

class PromptTemplateManager:
    def __init__(self, parent, settings_callback):
        """
        初始化提示词模板管理器
        :param parent: 父窗口
        :param settings_callback: 保存设置的回调函数
        """
        print(f"初始化PromptTemplateManager，父窗口类型: {type(parent)}")
        self.parent = parent
        self.settings_callback = settings_callback
        
        # 提示词模板数据
        self.prompt_templates = []
        self.default_prompt = ""
        
        # 创建UI组件
        self.frame = ttk.Frame(parent)
        print(f"已创建PromptTemplateManager的主框架")
        self.frame.pack(fill=tk.BOTH, expand=True)  # 确保frame可见
        print(f"已将主框架放入父容器")
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        print("开始创建PromptTemplateManager的界面组件")
        ttk.Label(self.frame, text="提示词模板管理", font=('微软雅黑', 12, 'bold')).pack(anchor=tk.W, padx=10, pady=10)
        ttk.Label(self.frame, text="根据联系人名称中的关键词选择不同的系统提示词").pack(anchor=tk.W, padx=10)
        print("已创建标题标签")
        
        # 创建两个框架区域
        templates_frame = ttk.LabelFrame(self.frame, text="关键词-提示词配置")
        templates_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        default_frame = ttk.LabelFrame(self.frame, text="默认提示词")
        default_frame.pack(fill=tk.X, padx=10, pady=10)
        print("已创建两个主要框架区域")
        
        # 创建表格显示当前配置
        self.tree = ttk.Treeview(templates_frame, columns=("关键词", "提示词"), show="headings", height=10)
        self.tree.heading("关键词", text="关键词")
        self.tree.heading("提示词", text="提示词")
        self.tree.column("关键词", width=150)
        self.tree.column("提示词", width=400)
        self.tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        print("已创建TreeView")
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(templates_frame, orient="vertical", command=self.tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.configure(yscrollcommand=scrollbar.set)
        print("已添加滚动条")
        
        # 添加操作按钮
        buttons_frame = ttk.Frame(templates_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="添加", command=self.add_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="编辑", command=self.edit_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="删除", command=self.delete_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="保存配置", command=self.save_templates).pack(side=tk.RIGHT, padx=5)
        print("已创建操作按钮")
        
        # 默认提示词区域
        ttk.Label(default_frame, text="当联系人名称不匹配任何关键词时使用:").pack(anchor=tk.W, padx=5, pady=5)
        self.default_prompt_text = tk.Text(default_frame, height=6, width=50, font=('微软雅黑', 10))
        self.default_prompt_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        print("已创建默认提示词文本区域")
        
        print("所有界面组件已创建完成")
        
    def load_templates(self, settings):
        """从设置加载提示词模板"""
        self.prompt_templates = settings.get('prompt_templates', [])
        self.default_prompt = settings.get('default_system_prompt', 
                                         '你是一个有用的助手。请用简短、友好的方式回答问题。')
        
        # 更新UI
        self.update_tree()
        self.default_prompt_text.delete("1.0", tk.END)
        self.default_prompt_text.insert("1.0", self.default_prompt)
        
    def update_tree(self):
        """更新树状表格显示"""
        # 清空树
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 添加模板数据
        for template in self.prompt_templates:
            keyword = template.get('keyword', '')
            prompt = template.get('prompt', '')
            # 显示提示词的前50个字符，后面用省略号
            display_prompt = prompt[:50] + "..." if len(prompt) > 50 else prompt
            self.tree.insert("", tk.END, values=(keyword, display_prompt))
            
    def add_template(self):
        """添加新的提示词模板"""
        # 创建对话框
        dialog = tk.Toplevel(self.parent)
        dialog.title("添加提示词模板")
        dialog.geometry("600x400")
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # 创建表单
        ttk.Label(dialog, text="关键词:").pack(anchor=tk.W, padx=10, pady=5)
        keyword_entry = ttk.Entry(dialog, width=50)
        keyword_entry.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(dialog, text="提示词:").pack(anchor=tk.W, padx=10, pady=5)
        prompt_text = tk.Text(dialog, height=10, width=50)
        prompt_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 按钮区域
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        def save_template():
            keyword = keyword_entry.get().strip()
            prompt = prompt_text.get("1.0", tk.END).strip()
            
            if not keyword:
                messagebox.showerror("错误", "关键词不能为空", parent=dialog)
                return
                
            if not prompt:
                messagebox.showerror("错误", "提示词不能为空", parent=dialog)
                return
                
            # 添加到模板列表
            self.prompt_templates.append({
                'keyword': keyword,
                'prompt': prompt
            })
            
            # 更新UI
            self.update_tree()
            dialog.destroy()
        
        ttk.Button(button_frame, text="保存", command=save_template).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)
        
    def edit_template(self):
        """编辑选中的提示词模板"""
        # 获取选中项
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showinfo("提示", "请先选择要编辑的项目")
            return
            
        # 获取选中项的索引
        index = self.tree.index(selected_item[0])
        if index >= len(self.prompt_templates):
            messagebox.showerror("错误", "无法获取选中项的数据")
            return
            
        template = self.prompt_templates[index]
        
        # 创建对话框
        dialog = tk.Toplevel(self.parent)
        dialog.title("编辑提示词模板")
        dialog.geometry("600x400")
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # 创建表单
        ttk.Label(dialog, text="关键词:").pack(anchor=tk.W, padx=10, pady=5)
        keyword_entry = ttk.Entry(dialog, width=50)
        keyword_entry.pack(fill=tk.X, padx=10, pady=5)
        keyword_entry.insert(0, template.get('keyword', ''))
        
        ttk.Label(dialog, text="提示词:").pack(anchor=tk.W, padx=10, pady=5)
        prompt_text = tk.Text(dialog, height=10, width=50)
        prompt_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        prompt_text.insert("1.0", template.get('prompt', ''))
        
        # 按钮区域
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        def save_template():
            keyword = keyword_entry.get().strip()
            prompt = prompt_text.get("1.0", tk.END).strip()
            
            if not keyword:
                messagebox.showerror("错误", "关键词不能为空", parent=dialog)
                return
                
            if not prompt:
                messagebox.showerror("错误", "提示词不能为空", parent=dialog)
                return
                
            # 更新模板
            self.prompt_templates[index] = {
                'keyword': keyword,
                'prompt': prompt
            }
            
            # 更新UI
            self.update_tree()
            dialog.destroy()
        
        ttk.Button(button_frame, text="保存", command=save_template).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)
        
    def delete_template(self):
        """删除选中的提示词模板"""
        # 获取选中项
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showinfo("提示", "请先选择要删除的项目")
            return
            
        # 获取选中项的索引
        index = self.tree.index(selected_item[0])
        if index >= len(self.prompt_templates):
            messagebox.showerror("错误", "无法获取选中项的数据")
            return
            
        # 确认删除
        if messagebox.askyesno("确认", "确定要删除该提示词模板吗?"):
            # 从列表中删除
            del self.prompt_templates[index]
            
            # 更新UI
            self.update_tree()
            
    def save_templates(self):
        """保存提示词模板配置"""
        # 获取默认提示词
        default_prompt = self.default_prompt_text.get("1.0", tk.END).strip()
        
        # 准备设置数据
        settings_data = {
            'prompt_templates': self.prompt_templates,
            'default_system_prompt': default_prompt
        }
        
        # 保存设置
        self.settings_callback(settings_data)
        messagebox.showinfo("成功", "提示词模板配置已保存")
        
    def get_frame(self):
        """获取UI框架"""
        # 由于在初始化时已经将frame放入parent，这里只需返回self以支持链式调用
        return self
        
    def get_system_prompt(self, contact_name):
        """
        根据联系人名称获取合适的系统提示词
        :param contact_name: 联系人名称
        :return: 匹配的系统提示词
        """
        print(f"PromptTemplateManager.get_system_prompt被调用，联系人: {contact_name}")
        print(f"当前模板数量: {len(self.prompt_templates)}")
        
        if not contact_name:
            print("未提供联系人名称，返回默认提示词")
            return self.default_prompt or ""  # 确保不返回None
            
        # 遍历所有模板，查找匹配的关键词
        for i, template in enumerate(self.prompt_templates):
            keyword = template.get('keyword', '')
            prompt = template.get('prompt', '')
            
            print(f"检查模板 {i+1}: 关键词 '{keyword}'")
            
            if keyword and keyword in contact_name:
                print(f"✓ 找到匹配! 关键词 '{keyword}' 在联系人名称 '{contact_name}' 中")
                if prompt is None:
                    prompt = ""
                    print("警告：匹配的提示词为None，已设置为空字符串")
                print(f"使用匹配的提示词: {prompt[:30]}..." if prompt and len(prompt) > 30 else prompt)
                return prompt
            else:
                print(f"✗ 不匹配: 关键词 '{keyword}' 不在联系人名称 '{contact_name}' 中")
                
        # 如果没有匹配的关键词，返回默认提示词
        # 确保default_prompt不为None
        if self.default_prompt is None:
            self.default_prompt = ""
            print("警告：默认提示词为None，已设置为空字符串")
        
        print(f"没有找到匹配的关键词，返回默认提示词: {self.default_prompt[:30]}..." if self.default_prompt and len(self.default_prompt) > 30 else self.default_prompt)
        return self.default_prompt 