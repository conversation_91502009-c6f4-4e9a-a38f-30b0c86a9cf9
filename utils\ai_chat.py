from langchain_openai import ChatOpenAI
from openai import OpenAI
import time
import pyautogui
import platform
import pyperclip
import re
from utils.call_tool import create_tools
import requests  # For Coze API requests
import json  # For JSON serialization/deserialization
from langchain_core.messages import ToolMessage
import asyncio
from pynput.mouse import <PERSON><PERSON>, Controller
from utils.image_processor import capture_screen_area  # Import for screen capture
from utils.call_tool import create_tools
from datetime import datetime


class AiChat:
    def __init__(self, api_settings, stop_flag=None):
        """
        初始化AI聊天工具
        :param api_settings: API设置参数字典
        :param stop_flag: 停止标志引用，用于检查是否应该停止生成或打字
        """
        self.provider = api_settings.get('provider', 'openai')  # 'openai' 或 'coze'
        
        # OpenAI 相关参数
        self.api_key = api_settings.get('api_key', '')
        self.api_base_url = api_settings.get('api_base_url', 'https://api.openai.com')
        self.model_name = api_settings.get('model_name', 'gpt-3.5-turbo')
        
        # Coze Space 相关参数
        self.bot_id = api_settings.get('bot_id', '7504237***********')
        self.token = api_settings.get('token', 'pat_wongEhcYRY7**********')

        # AI Brain 相关参数
        self.ai_brain_url = api_settings.get('ai_brain_url', '')
        self.ai_brain_key = api_settings.get('ai_brain_key', '')
        
        # 通用参数
        self.system_prompt = api_settings.get('system_prompt', '你是一个有用的助手。请用简短、友好的方式回答问题。')
        self.delay = api_settings.get('delay', 0.1)
        self.debug_mode = api_settings.get('debug_mode', False)
        
        # 停止标志引用
        self.stop_flag = stop_flag
    
    def _check_stop_flag(self):
        """检查停止标志 - 处理可能是函数或值的情况"""
        if self.stop_flag is None:
            return False
        if callable(self.stop_flag):
            return self.stop_flag()
        return self.stop_flag
        
    def generate_reply(self, conversation_history,input_area):
        """根据对话历史生成回复"""
        # 首先检查停止标志
        if self._check_stop_flag():
            if self.debug_mode:
                print("检测到停止请求，中断回复生成过程")
            return "回复生成已中断"
            
        if self.debug_mode:
            print(f"【生成回复】基于{len(conversation_history)}条对话历史生成回复...")
            print(f"【系统提示词】{self.system_prompt[:50]}..." if len(self.system_prompt) > 50 else self.system_prompt)
            print(f"【使用服务】{self.provider}")

        
        # 创建一个新的对话历史，在开头添加系统提示词
        enhanced_history = [{"role": "system", "content": self.system_prompt}]
        
        # 添加实际的对话历史
        enhanced_history.extend(conversation_history)

        
        # 确保history中不包含position等额外信息，只保留role和content
        cleaned_history = []
        print(f"enhanced_history: {enhanced_history}")
        for msg in enhanced_history:
            if msg["role"] == "user":
                cleaned_history.append({"role":"user","content":msg["content"],"timestamp":msg["timestamp"]})
            elif msg["role"] == "assistant":
                if self.provider == 'openai':
                    cleaned_history.append({"role":"assistant","content":msg["content"],"tool_calls":msg["tool_calls"],"timestamp":msg["timestamp"]})
                else:
                    cleaned_history.append({"role":"assistant","content":msg["content"],"tool_calls":[],"timestamp":msg["timestamp"]})
            elif msg["role"] == "tool":
                cleaned_history.append({"role":"tool","content":msg["content"],"tool_call_id":msg["tool_call_id"],"timestamp":msg["timestamp"]})
            else:
                cleaned_history.append({"role": msg["role"], "content": msg["content"]})
        
        try:
            if self.provider == 'openai':
                return asyncio.run(self._generate_reply_openai(cleaned_history,input_area))
            elif self.provider == 'coze':
                return self._generate_reply_coze(cleaned_history)
            elif self.provider == 'ai_brain':
                return asyncio.run(self._generate_reply_ai_brain(cleaned_history,input_area))
            else:
                raise ValueError(f"不支持的提供商: {self.provider}")
        except Exception as e:
            error_msg = f"生成回复时出错: {str(e)}"
            if self.debug_mode:
                print(error_msg)
            return f"抱歉，{error_msg}"
        
    async def get_userid_by_ai_brain(self,ai_brain_url,ai_brain_key,wxid,name):
        """使用AI Brain API获取用户ID"""
        url = f"{ai_brain_url}/wechat_query_user"
        headers = {
            "Authorization": f"Bearer {ai_brain_key}",
            "Content-Type": "application/json"
        }
        data = {
            "wxid":wxid,
            "name":name,
            "source":"wechat"
        }
        res = requests.post(url=url,headers=headers,data=json.dumps(data))
        if res.json()['code'] == 200:
            return res.json()['userid']
        else:
            return ""
    
    # 07091 新增 ai_brain 接口
    async def _generate_reply_ai_brain(self, cleaned_history,input_area):
        """使用AI Brain API生成回复"""
        # 检查停止标志
        if self._check_stop_flag():
            if self.debug_mode:
                print("检测到停止请求，中断AI Brain回复生成过程")
            return "回复生成已中断"
        if input_area:
                input_x1, input_y1, input_x2, input_y2 = input_area
                # 计算输入区域的中心点
                target_x = input_x1 + (input_x2 - input_x1) // 2
                target_y = input_y1 + (input_y2 - input_y1) // 2
                if self.debug_mode:
                    print(f"移动鼠标到输入区中心点: ({target_x}, {target_y})")
        else:
            # 如果没有正确选择输入区域，提示错误
            if self.debug_mode:
                print("未提供输入区域坐标")
            return False
            
        pyautogui.moveTo(target_x, target_y)
        time.sleep(0.2)
        
        if self.debug_mode:
            print("点击激活输入框")

        if platform.system() == 'Darwin':  # macOS
            pyautogui.click()  # 点击激活输入框
            time.sleep(0.2)
            pyautogui.click()
        else:
            mouse = Controller()
            mouse.click(Button.left)
            time.sleep(0.2)
            mouse.click(Button.left)


        with open('settings.json', 'r',encoding='utf-8') as f:
            setting = json.load(f)
            ai_brain_url = setting["ai_brain_url07091"]
            ai_brain_key = setting["ai_brain_key07091"]

        # 准备发送请求数据
        # 先从唯一标识区获取数据，调用ocr识别，获取wxid
        
        wxid = ""
        name = ""
        
        # 获取唯一标识区域和名称区域的坐标
        unique_id_area = setting.get("unique_id_area")
        contact_name_area = setting.get("contact_name_area")
        
        # 导入消息提取器
        from ocr.message_extractor import MessageExtractor
        message_extractor = MessageExtractor()
        
        # 提取wxid
        if unique_id_area:
            # 截取唯一标识区域的图像
            unique_id_img = capture_screen_area(
                unique_id_area[0], 
                unique_id_area[1], 
                unique_id_area[2], 
                unique_id_area[3], 
                []
            )
            
            # 调用extract_wxid提取wxid
            ocr_params = {'debug_mode': self.debug_mode}
            wxid = message_extractor.extract_wxid(unique_id_img, ocr_params)
            if self.debug_mode:
                print(f"从唯一标识区提取的wxid: {wxid}")
        
        # 提取联系人名称
        if contact_name_area:
            # 截取联系人名称区域的图像
            name_img = capture_screen_area(
                contact_name_area[0], 
                contact_name_area[1], 
                contact_name_area[2], 
                contact_name_area[3], 
                []
            )
            
            # 调用extract_contact_name提取联系人名称
            ocr_params = {'debug_mode': self.debug_mode}
            name = message_extractor.extract_contact_name(name_img, ocr_params)
            if self.debug_mode:
                print(f"从名称区提取的联系人名称: {name}")
        
        # 获取用户ID（如果wxid和name都存在）
        ai_brain_userid = ""
        if wxid and name:
            try:
                ai_brain_userid = await self.get_userid_by_ai_brain(ai_brain_url, ai_brain_key, wxid, name)
                if self.debug_mode:
                    print(f"获取到的用户ID: {ai_brain_userid}")
            except Exception as e:
                if self.debug_mode:
                    print(f"获取用户ID时出错: {str(e)}")
        else:
            print("未获取到wxid或name")
            return None
        
        # 处理cleaned_history
        cleaned_history = cleaned_history[1:]

        url = f"{ai_brain_url}/wechat_chat"
        headers = {
            "Authorization": f"Bearer {ai_brain_key}",
            "Content-Type": "application/json"
        }
        data = {
            "messages": cleaned_history,
            "name": name,
            "userid": ai_brain_userid,
            "wxid": wxid,
            "source": "wechat"
        }
        print(f"AI Brain 发送请求数据: {data}")
        res = requests.post(url=url, headers=headers, data=json.dumps(data))
        print(f"AI Brain 返回数据: {res.json()}")
        # 执行actions字段中的action
        tools = create_tools()
        for action in res.json()['actions']:
            try:
                if action["action"] == "send_emoji":
                    print(f"执行send_emoji: {action['params']['emoji_name']}")
                    tools[0].invoke(action["params"]["emoji_name"])
                elif action["action"] == "send_file":
                    print(f"执行send_file: {action['params']['file_path']}")
                    tools[1].invoke(action["params"]["file_path"])
                else:
                    print(f"不支持的action: {action}")
            except Exception as e:
                print(f"执行action时出错: {str(e)}")
        return [{"role":"assistant","content":res.json()['content'],"timestamp":int(time.time())}]
    
    async def _generate_reply_openai(self, cleaned_history,input_area):
        """使用OpenAI API生成回复"""
        # 检查停止标志
        if self._check_stop_flag():
            if self.debug_mode:
                print("检测到停止请求，中断OpenAI回复生成过程")
            return "回复生成已中断"
        
        if input_area:
                input_x1, input_y1, input_x2, input_y2 = input_area
                # 计算输入区域的中心点
                target_x = input_x1 + (input_x2 - input_x1) // 2
                target_y = input_y1 + (input_y2 - input_y1) // 2
                if self.debug_mode:
                    print(f"移动鼠标到输入区中心点: ({target_x}, {target_y})")
        else:
            # 如果没有正确选择输入区域，提示错误
            if self.debug_mode:
                print("未提供输入区域坐标")
            return False
            
        pyautogui.moveTo(target_x, target_y)
        time.sleep(0.2)
        
        if self.debug_mode:
            print("点击激活输入框")

        if platform.system() == 'Darwin':  # macOS
            pyautogui.click()  # 点击激活输入框
            time.sleep(0.2)
            pyautogui.click()
        else:
            mouse = Controller()
            mouse.click(Button.left)
            time.sleep(0.2)
            mouse.click(Button.left)

        retuen_message_list = []

        # 从settings.json中获取emojis_or_file_prompt
        with open('settings.json', 'r',encoding='utf-8') as f:
            setting = json.load(f)
            emojis_or_file_prompt = setting["emojis_or_file_prompt"]

        if cleaned_history[0]["role"] == "system":
            cleaned_history[0]["content"] = cleaned_history[0]["content"] + "\n" + emojis_or_file_prompt
        else:
            cleaned_history.insert(0, {"role":"system","content":emojis_or_file_prompt})
        
        tools = create_tools()
        tool_name_to_tool = {tool.name: tool for tool in tools}
        llm = ChatOpenAI(api_key=self.api_key, base_url=self.api_base_url,model=self.model_name)
        llm_with_tools = llm.bind_tools(tools)

        result_text = ""
        print(f"cleaned_history:{cleaned_history}")

        while True:
            ai_msg = await llm_with_tools.ainvoke(cleaned_history)
            result_text += ai_msg.content
            cleaned_history.append(ai_msg)
            retuen_message_list.append({
                "role":"assistant",
                "content":ai_msg.content,
                "tool_calls":ai_msg.tool_calls,
                "timestamp": int(time.time()) 
            })
            print(ai_msg)
            if not ai_msg.tool_calls:
                retuen_message_list[-1]["content"] = result_text
                return retuen_message_list
            for tool_call in ai_msg.tool_calls:
                tool_name = tool_call["name"]
                tool_args = tool_call["args"]
                tool_call_id = tool_call["id"]
                requested_tool = tool_name_to_tool[tool_name]
                call_result = requested_tool.invoke(tool_args)
                print(call_result)
                cleaned_history.append(ToolMessage(content=call_result,tool_call_id=tool_call_id))
                retuen_message_list.append({
                    "role":"tool",
                    "content":call_result,
                    "tool_call_id":tool_call_id,
                    "timestamp": int(time.time()) 
                })


    def _generate_reply_coze(self, cleaned_history):
        """使用Coze Space API生成回复"""
        # 检查停止标志
        if self._check_stop_flag():
            if self.debug_mode:
                print("检测到停止请求，中断Coze回复生成过程")
            return "回复生成已中断"
            
        # TODO: 实现Coze Space API调用逻辑
        if self.debug_mode:
            print(f"向Coze Space API发送请求，使用bot_id: {self.bot_id}")
        
        try:
            # 以下是Coze Space API调用的示例骨架，需要根据实际API文档进行实现
            # 1. 准备请求数据
            messages = []
            for msg in cleaned_history:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"],
                    "content_type": "text"
                })
            
            data = {
                "bot_id": self.bot_id,
                "user_id": "123123",
                "stream": False,
                "auto_save_history":True,
                "additional_messages":messages
            }
            coze_url = "https://api.coze.cn/v3/chat"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.token}"
            }

            res = requests.post(url=coze_url,data=json.dumps(data),headers=headers)
            while True:
                # 检查停止标志
                if self._check_stop_flag():
                    if self.debug_mode:
                        print("检测到停止请求，中断Coze回复生成轮询")
                    return "回复生成已中断"
                    
                step1_url = f"https://api.coze.cn/v3/chat/retrieve?conversation_id={res.json()['data']['conversation_id']}&chat_id={res.json()['data']['id']}"
                res1 = requests.get(url=step1_url,headers=headers)
                if res1.json()['data']['status'] == "in_progress":
                    time.sleep(1)
                    print("coze 生成中.....")
                    continue
                if res1.json()['data']['status'] == "completed":
                    step2_url = f"https://api.coze.cn/v3/chat/message/list?conversation_id={res.json()['data']['conversation_id']}&chat_id={res.json()['data']['id']}"
                    res2 = requests.get(url=step2_url, headers=headers)
                    print("coze 生成完成",res2.json()['data'][1]['content'])
                    return [{"role":"assistant","content":res2.json()['data'][1]['content'],"timestamp":int(time.time())}]
                if res1.json()['data']['status'] in ["failed","cancelled","requires_action"]:
                    print("coze 生成失败")
                    return None



        except Exception as e:
            if self.debug_mode:
                print(f"Coze API调用错误: {str(e)}")
            return None
    
    def split_into_sentences(self, message):
        """根据标点符号将消息分成多个句子"""
        # 使用正则表达式按照句号、问号、感叹号、分号进行分割
        # 保留分隔符在句子末尾
        pattern = r'([^。？！；?!;～~]+[。？！；?!;～~])'
        sentences = re.findall(pattern, message)
        
        # 如果分割后没有句子，或者有剩余文本，则添加原始消息
        if not sentences or ''.join(sentences) != message:
            # 处理消息末尾没有标点符号的情况
            remaining = message
            for sentence in sentences:
                remaining = remaining.replace(sentence, '', 1)
            if remaining.strip():
                sentences.append(remaining.strip())
        
        return [s.strip() for s in sentences if s.strip()]
    
    def type_reply(self, message, input_area=None, controller=None):
        """模拟键盘输入回复"""
        message = message.replace("(","").replace(")","").replace("（","").replace("）","").replace("。","")
        if self.debug_mode:
            print(f"【输入回复】: {message}")
        # 使用PyAutoGUI模拟键盘输入
        try:
            # 使用框选的输入区坐标
            if input_area:
                input_x1, input_y1, input_x2, input_y2 = input_area
                # 计算输入区域的中心点
                target_x = input_x1 + (input_x2 - input_x1) // 2
                target_y = input_y1 + (input_y2 - input_y1) // 2
                if self.debug_mode:
                    print(f"移动鼠标到输入区中心点: ({target_x}, {target_y})")
            else:
                # 如果没有正确选择输入区域，提示错误
                if self.debug_mode:
                    print("未提供输入区域坐标")
                return False
            
            # 检查停止标志
            if self._check_stop_flag():
                if self.debug_mode:
                    print("检测到停止请求，中断输入过程")
                return False
                
            pyautogui.moveTo(target_x, target_y)
            time.sleep(0.5)
            
            if self.debug_mode:
                print("点击激活输入框")
            if platform.system() == 'Darwin':  # macOS
                pyautogui.click()  # 点击激活输入框
            else:
                mouse = Controller()
                mouse.click(Button.left)
            time.sleep(0.5)  # 等待输入框激活
            
            # 获取用户设置的延迟
            delay = self.delay
            
            # 保存原始剪贴板内容
            original_clipboard = pyperclip.paste()
            
            # 将消息按句子分割
            sentences = self.split_into_sentences(message)
            if self.debug_mode:
                print(f"消息被分割为{len(sentences)}个句子")
            
            # 对每个句子分别处理
            for idx, sentence in enumerate(sentences):
                # 检查停止标志
                if self._check_stop_flag():
                    if self.debug_mode:
                        print(f"检测到停止请求，中断输入过程（已完成{idx}/{len(sentences)}个句子）")
                    # 恢复原始剪贴板内容
                    pyperclip.copy(original_clipboard)
                    return False
                    
                if self.debug_mode:
                    print(f"处理第{idx+1}个句子: {sentence}")
                
                # 逐字或逐块粘贴每个句子，保留延迟效果
                chunk_size = 5  # 每次粘贴5个字符
                if self.debug_mode:
                    print(f"开始输入句子，字符间延迟: {delay}秒")
                
                for i in range(0, len(sentence), chunk_size):
                    # 检查停止标志
                    if self._check_stop_flag():
                        if self.debug_mode:
                            print(f"检测到停止请求，中断输入过程（句子'{sentence}'的第{i}/{len(sentence)}个字符）")
                        # 恢复原始剪贴板内容
                        pyperclip.copy(original_clipboard)
                        return False
                        
                    # 取一个字符或一小块文本
                    chunk = sentence[i:i+chunk_size]
                    chunk = chunk.replace(""","").replace(""","").replace("'","").replace("'","").replace('"','').replace("'",'')
                    
                    # 复制到剪贴板
                    pyperclip.copy(chunk)

                    # 添加延迟
                    time.sleep(delay)
                    
                    # 粘贴操作
                    if platform.system() == 'Darwin':  # macOS
                        pyautogui.hotkey('command', 'v')
                    else:  # Windows/Linux
                        pyautogui.hotkey('ctrl', 'v')
                    
                    # 添加延迟
                    # time.sleep(delay)
                
                # 发送当前句子
                time.sleep(0.5)
                if self.debug_mode:
                    print(f"发送句子: {sentence}")
                pyautogui.press('enter')
                
                # 每个句子之间添加延迟
                time.sleep(delay * 2)  # 句子间延迟设置为字符间延迟的两倍
            
            # 恢复原始剪贴板内容
            pyperclip.copy(original_clipboard)
            
            if self.debug_mode:
                print("【所有句子已发送】")
            
            # 输入完成后，检查是否有新消息需要处理
            if controller:
                if self.debug_mode:
                    print("输入完成，等待短暂时间后检查聊天区是否有新消息...")
                
                # 等待一小段时间，让用户有机会发送新消息
                time.sleep(1.0)
                
                # 保持聊天区处理标志为True，防止切换到联系人区域检测
                controller.is_exec_chat_area = True
                
                # 确保消息跟踪集合已初始化
                if not hasattr(controller, 'processed_message_hashes'):
                    controller.processed_message_hashes = set()
                    if self.debug_mode:
                        print("初始化消息跟踪集合")
                
                # 记录当前跟踪集合大小
                if self.debug_mode:
                    print(f"递归检查前已跟踪消息数量: {len(controller.processed_message_hashes)}")
                
                # 在递归处理前，先将当前AI回复添加到对话历史
                # 这确保后续处理新消息时能看到完整的上下文
                current_time = int(time.time())
                ai_message = {
                    "role": "assistant", 
                    "content": message, 
                    "timestamp": current_time,
                    "is_temp_record": True,  # 标记为临时记录，避免重复添加
                    "tool_calls": []  # 添加空的tool_calls字段
                }
                
                # 检查是否已存在相同内容的回复(防止重复添加)
                should_add = True
                for msg in controller.conversation_history:
                    if msg.get("role") == "assistant" and msg.get("content") == message:
                        should_add = False
                        if self.debug_mode:
                            print("检测到相同内容的AI回复已存在于历史记录中，不再重复添加")
                        break
                
                if should_add:
                    controller.conversation_history.append(ai_message)
                    if self.debug_mode:
                        print(f"已将当前AI回复临时添加到历史记录: {message[:30]}..." if len(message) > 30 else message)
                
                # 递归处理聊天区，检查是否有新消息
                controller.process_chat_area()
                
                # 递归处理后移除临时记录
                for i in range(len(controller.conversation_history) - 1, -1, -1):
                    if controller.conversation_history[i].get("is_temp_record", False):
                        if self.debug_mode:
                            print("移除临时添加的AI回复记录")
                        controller.conversation_history.pop(i)
                        break
                
                if self.debug_mode:
                    print(f"递归检查后已跟踪消息数量: {len(controller.processed_message_hashes)}")
            
            return True
        except Exception as e:
            if self.debug_mode:
                print(f"输入回复过程出错: {str(e)}")
            # 确保恢复原始剪贴板内容
            try:
                pyperclip.copy(original_clipboard)
            except:
                pass
            return False

    def generate_text(self, prompt):
        """使用OpenAI API生成文本"""
        try:
            # 从settings.json中获取系统提示词
            with open('settings.json', 'r',encoding='utf-8') as f:
                setting = json.load(f)
            
            api_key = setting["api_key"]
            base_url = setting["api_base_url"]
            model_name = setting["model_name"]

            messages = [
                {"role":"system","content":prompt},
                {"role":"user","content":"开始吧"}
            ]

            openai_client = OpenAI(api_key=api_key, base_url=base_url)
            stream = openai_client.chat.completions.create(
                model=model_name,
                messages=messages,
                stream=False,  # 启用流式输出
            )
            return stream.choices[0].message.content
        except Exception as e:
            error_msg = f"生成文本时出错: {str(e)}"
            if self.debug_mode:
                print(error_msg)
            return None 