import Levenshtein

def sentence_similarity_by_edit_distance(sentence1: str, sentence2: str) -> float:
    """
    使用编辑距离（Levenshtein Distance）计算两个字符串的相似度。
    适用于判断OCR识别后有少量字符差异的句子相似度。

    Args:
        sentence1 (str): 第一个句子。
        sentence2 (str): 第二个句子。

    Returns:
        float: 句子的相似度，0表示完全不相似，1表示完全相同。
               基于 1 - (编辑距离 / 较长字符串长度)。
               如果两个句子都为空，则返回1.0（完全相同）；如果只有一个为空，则返回0.0。
    """
    s1_len = len(sentence1)
    s2_len = len(sentence2)

    # 处理空字符串情况
    if s1_len == 0 and s2_len == 0:
        return 1.0  # 都为空，认为完全相同
    if s1_len == 0 or s2_len == 0:
        return 0.0  # 只有一个为空，认为完全不相似

    # 计算编辑距离
    distance = Levenshtein.distance(sentence1, sentence2)

    # 较长字符串的长度
    max_len = max(s1_len, s2_len)

    # 避免除以0的情况，虽然在前面的空字符串处理中已经避免了
    if max_len == 0:
        return 0.0

    # 计算相似度
    similarity = 1 - (distance / max_len)

    # 确保相似度在0到1之间，浮点运算可能导致微小误差
    return max(0.0, min(1.0, similarity))

# --- 示例用法 ---
# if __name__ == "__main__":
#     print(sentence_similarity_by_edit_distance("哈哈", "哈哈！"))
#     # OCR 矫正场景
#     ocr_sentence1 = "北京是一个美丽的城市"
#     ocr_sentence1_error1 = "北京是一个美丽的城巿" # 市 vs 巿 (形似字)
#     ocr_sentence1_error2 = "北京是一个 美丽的城市" # 多了一个空格
#     ocr_sentence1_error3 = "北京是一个美丽的城市啊" # 多一个字
#     ocr_sentence1_error4 = "北京是个美丽的城市" # 少一个字

#     # 语义完全不相关的句子
#     s_unrelated1 = "上海是一个繁华的都市"
#     s_unrelated2 = "这完全是两回事，和之前说的不是一码事"
#     s_unrelated3 = "今天天气真好"

#     print("--- 针对 OCR 细微差异 ---")
#     print(f"'{ocr_sentence1}' 和 '{ocr_sentence1_error1}' 的相似度: {sentence_similarity_by_edit_distance(ocr_sentence1, ocr_sentence1_error1):.4f}") # 期待高相似度
#     print(f"'{ocr_sentence1}' 和 '{ocr_sentence1_error2}' 的相似度: {sentence_similarity_by_edit_distance(ocr_sentence1, ocr_sentence1_error2):.4f}") # 期待高相似度
#     print(f"'{ocr_sentence1}' 和 '{ocr_sentence1_error3}' 的相似度: {sentence_similarity_by_edit_distance(ocr_sentence1, ocr_sentence1_error3):.4f}") # 期待较高相似度
#     print(f"'{ocr_sentence1}' 和 '{ocr_sentence1_error4}' 的相似度: {sentence_similarity_by_edit_distance(ocr_sentence1, ocr_sentence1_error4):.4f}") # 期待较高相似度
#     print(f"'{ocr_sentence1}' 和 '{ocr_sentence1}' 的相似度: {sentence_similarity_by_edit_distance(ocr_sentence1, ocr_sentence1):.4f}") # 期待 1.0

#     print("\n--- 针对语义不相关句子 ---")
#     print(f"'{ocr_sentence1}' 和 '{s_unrelated1}' 的相似度: {sentence_similarity_by_edit_distance(ocr_sentence1, s_unrelated1):.4f}") # 期待较低相似度
#     print(f"'{ocr_sentence1}' 和 '{s_unrelated2}' 的相似度: {sentence_similarity_by_edit_distance(ocr_sentence1, s_unrelated2):.4f}") # 期待很低相似度
#     print(f"'{s_unrelated2}' 和 '{s_unrelated3}' 的相似度: {sentence_similarity_by_edit_distance(s_unrelated2, s_unrelated3):.4f}") # 期待很低相似度

#     print("\n--- 其他边缘情况 ---")
#     print(f"'' 和 '' 的相似度: {sentence_similarity_by_edit_distance('', ''):.4f}")
#     print(f"'abc' 和 '' 的相似度: {sentence_similarity_by_edit_distance('abc', ''):.4f}")
#     print(f"'abcd' 和 'abce' 的相似度: {sentence_similarity_by_edit_distance('abcd', 'abce'):.4f}")