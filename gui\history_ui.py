import tkinter as tk
from tkinter import messagebox, scrolledtext, ttk, filedialog
from datetime import datetime
import os

class HistoryUI:
    def __init__(self, root, conversations, save_callback=None):
        """
        初始化对话历史显示界面
        :param root: 父窗口
        :param conversations: 对话历史字典 {联系人: 对话历史}
        :param save_callback: 保存对话的回调函数
        """
        self.root = root
        self.conversations = conversations
        self.save_callback = save_callback
        self.history_window = None
        
    def show_window(self):
        """显示对话历史窗口"""
        if not self.conversations:
            messagebox.showinfo("对话历史", "当前没有任何对话历史记录")
            return
            
        # 创建一个新窗口显示对话历史
        self.history_window = tk.Toplevel(self.root)
        self.history_window.title("对话历史")
        self.history_window.geometry("800x600")
        self.history_window.configure(bg="#f5f5f7")
        
        # 创建整体的框架
        main_frame = ttk.Frame(self.history_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 设置分割窗口
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 创建联系人列表框架
        contact_frame = ttk.Frame(paned_window)
        paned_window.add(contact_frame, weight=1)
        
        # 联系人标题
        contact_label = ttk.Label(contact_frame, text="联系人列表", font=('微软雅黑', 12, 'bold'))
        contact_label.pack(pady=(5,10), padx=5, anchor='w')
        
        # 创建搜索框
        search_frame = ttk.Frame(contact_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var)
        search_entry.pack(fill=tk.X, side=tk.LEFT, expand=True)
        
        search_button = ttk.Button(search_frame, text="搜索")
        search_button.pack(side=tk.RIGHT, padx=(5,0))
        
        # 创建联系人列表框
        contact_list_frame = ttk.Frame(contact_frame)
        contact_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        contact_scrollbar = ttk.Scrollbar(contact_list_frame)
        contact_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置联系人列表
        contact_listbox = tk.Listbox(contact_list_frame, font=('微软雅黑', 11), 
                                     bg="#ffffff", fg="#333333",
                                     selectbackground="#0078d7", selectforeground="#ffffff",
                                     activestyle='dotbox', 
                                     yscrollcommand=contact_scrollbar.set)
        contact_listbox.pack(fill=tk.BOTH, expand=True)
        contact_scrollbar.config(command=contact_listbox.yview)
        
        # 添加所有联系人到列表
        for contact in sorted(self.conversations.keys()):
            contact_listbox.insert(tk.END, contact)
        
        # 创建对话内容框架
        content_frame = ttk.Frame(paned_window)
        paned_window.add(content_frame, weight=3)
        
        # 对话内容标题
        content_label = ttk.Label(content_frame, text="对话内容", font=('微软雅黑', 12, 'bold'))
        content_label.pack(pady=(5,10), padx=5, anchor='w')
        
        # 创建滚动文本框框架
        history_frame = ttk.Frame(content_frame)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        history_scrollbar = ttk.Scrollbar(history_frame)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建滚动文本框
        history_text = scrolledtext.ScrolledText(history_frame, font=('微软雅黑', 11), 
                                               bg="#ffffff", fg="#333333", 
                                               relief=tk.FLAT, borderwidth=0,
                                               yscrollcommand=history_scrollbar.set)
        history_text.pack(fill=tk.BOTH, expand=True)
        history_scrollbar.config(command=history_text.yview)
        
        # 配置标签和内容样式
        history_text.tag_configure("user_tag", foreground="#0066cc", font=('微软雅黑', 11, 'bold'))
        history_text.tag_configure("user_content", foreground="#333333", font=('微软雅黑', 11))
        history_text.tag_configure("assistant_tag", foreground="#cc0066", font=('微软雅黑', 11, 'bold'))
        history_text.tag_configure("assistant_content", foreground="#333333", font=('微软雅黑', 11))
        history_text.tag_configure("timestamp", foreground="#666666", font=('微软雅黑', 9, 'italic'))
        history_text.tag_configure("message_box", background="#f0f0f0", relief=tk.GROOVE, borderwidth=1)
        history_text.tag_configure("separator", foreground="#dddddd")
        
        # 定义搜索联系人的功能
        def search_contacts():
            search_term = search_var.get().strip().lower()
            contact_listbox.delete(0, tk.END)
            
            for contact in sorted(self.conversations.keys()):
                if search_term in contact.lower():
                    contact_listbox.insert(tk.END, contact)
        
        search_button.config(command=search_contacts)
        search_entry.bind('<Return>', lambda e: search_contacts())
        
        # 定义选择联系人时的回调函数
        def on_contact_select(event):
            selected_indices = contact_listbox.curselection()
            if selected_indices:
                selected_contact = contact_listbox.get(selected_indices[0])
                # 清空当前内容
                history_text.delete(1.0, tk.END)
                # 显示选中联系人的对话历史
                conversation = self.conversations.get(selected_contact, [])
                
                # 添加联系人标题
                history_text.insert(tk.END, f"与 {selected_contact} 的对话历史\n\n", "assistant_tag")
                
                for i, msg in enumerate(conversation):
                    role = "用户" if msg["role"] == "user" else "助手"
                    content = msg["content"]
                    timestamp = msg.get("timestamp", 0)
                    time_str = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S") if timestamp else "未知时间"
                    
                    # 添加消息框
                    history_text.insert(tk.END, f"\n")
                    
                    # 显示时间戳
                    history_text.insert(tk.END, f"{time_str}\n", "timestamp")
                    
                    # 使用不同颜色区分角色
                    if role == "用户":
                        history_text.insert(tk.END, f"[{role}]: ", "user_tag")
                        history_text.insert(tk.END, f"{content}\n", "user_content")
                    else:
                        history_text.insert(tk.END, f"[{role}]: ", "assistant_tag")
                        history_text.insert(tk.END, f"{content}\n", "assistant_content")
                    
                    # 添加分隔线
                    if i < len(conversation) - 1:
                        history_text.insert(tk.END, f"\n{'-'*80}\n", "separator")
                
                # 滚动到顶部
                history_text.see("1.0")
        
        # 绑定选择事件
        contact_listbox.bind('<<ListboxSelect>>', on_contact_select)
        
        # 按钮框架
        button_frame = ttk.Frame(self.history_window)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        
        # 添加导出按钮
        def export_history():
            selected_indices = contact_listbox.curselection()
            if not selected_indices:
                messagebox.showinfo("导出历史", "请先选择一个联系人")
                return
                
            selected_contact = contact_listbox.get(selected_indices[0])
            conversation = self.conversations.get(selected_contact, [])
            
            if not conversation:
                messagebox.showinfo("导出历史", f"联系人 {selected_contact} 没有对话历史")
                return
                
            # 选择保存路径
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                initialfile=f"{selected_contact}_对话历史.txt"
            )
            
            if not file_path:
                return  # 用户取消了保存
                
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"== {selected_contact} 的对话历史 ==\n\n")
                    for i, msg in enumerate(conversation):
                        role = "用户" if msg["role"] == "user" else "助手"
                        content = msg["content"]
                        timestamp = msg.get("timestamp", 0)
                        time_str = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S") if timestamp else "未知时间"
                        
                        f.write(f"[{time_str}]\n")
                        f.write(f"[{role}]: {content}\n\n")
                        if i < len(conversation) - 1:
                            f.write(f"{'-'*40}\n\n")
                        
                messagebox.showinfo("导出成功", f"对话历史已保存到:\n{file_path}")
            except Exception as e:
                messagebox.showerror("导出失败", f"保存文件时出错:\n{str(e)}")
        
        # 添加删除功能
        def delete_history():
            selected_indices = contact_listbox.curselection()
            if not selected_indices:
                messagebox.showinfo("删除历史", "请先选择一个联系人")
                return
                
            selected_contact = contact_listbox.get(selected_indices[0])
            
            # 确认删除
            confirm = messagebox.askyesno("确认删除", 
                                         f"确定要删除与 {selected_contact} 的所有对话历史吗？\n此操作不可恢复。")
            if not confirm:
                return
            
            # 删除联系人的对话历史
            if selected_contact in self.conversations:
                # 从内存中删除
                del self.conversations[selected_contact]
                
                # 更新联系人列表
                contact_listbox.delete(selected_indices[0])
                
                # 清空当前显示的聊天内容
                history_text.delete(1.0, tk.END)
                
                # 如果有回调函数，通知主程序更新存储
                if self.save_callback:
                    self.save_callback(selected_contact, [])
                    
                    # 尝试直接删除文件
                    try:
                        # 使用与 storage_manager.save_conversation 相同的命名规则
                        clear_name = selected_contact.replace('.', '_').replace('/', '_').replace('\\', '_')
                        file_path = os.path.join("chat_history", f"{clear_name}.json")
                        
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            print(f"成功删除聊天历史文件: {file_path}")
                    except Exception as e:
                        print(f"删除聊天历史文件时出错: {str(e)}")
                
                messagebox.showinfo("删除成功", f"已删除与 {selected_contact} 的所有对话历史")
                
                # 如果联系人列表还有内容，选中第一个
                if contact_listbox.size() > 0:
                    contact_listbox.selection_set(0)
                    contact_listbox.event_generate('<<ListboxSelect>>')
        
        # 添加按钮
        # 删除按钮放在导出按钮右侧
        delete_btn = ttk.Button(button_frame, text="删除选中的对话历史", command=delete_history)
        delete_btn.pack(side=tk.RIGHT, padx=5, pady=5)
        
        export_btn = ttk.Button(button_frame, text="导出选中的对话历史", command=export_history)
        export_btn.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # 添加关闭按钮
        close_btn = ttk.Button(button_frame, text="关闭", command=self.close_window)
        close_btn.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # 如果已有联系人，默认选择第一个
        if contact_listbox.size() > 0:
            contact_listbox.selection_set(0)
            contact_listbox.event_generate('<<ListboxSelect>>')
            
    def close_window(self):
        """关闭对话历史窗口"""
        if self.history_window:
            self.history_window.destroy()
            self.history_window = None 