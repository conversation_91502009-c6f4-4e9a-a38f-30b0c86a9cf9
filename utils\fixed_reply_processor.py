import json
import os
import time
import threading
import platform
import pyautogui
import pyperclip
from pynput.mouse import <PERSON><PERSON>, Controller as Mouse<PERSON>ontroller
from pynput.keyboard import Key, Controller as KeyboardController


class FixedReplyProcessor:
    """固定回复处理器"""

    def __init__(self, settings_file="settings.json"):
        """
        初始化固定回复处理器
        :param settings_file: 设置文件路径
        """
        self.settings_file = settings_file
        self.rules = []
        self.is_processing = False
        self.input_area = None  # 输入区域坐标
        self.load_rules()
    
    def load_rules(self):
        """加载规则配置"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.rules = settings.get('fixed_reply_rules', [])
                    # 只加载启用的规则
                    self.rules = [rule for rule in self.rules if rule.get('enabled', True)]
        except Exception as e:
            print(f"加载固定回复规则失败: {str(e)}")
            self.rules = []
    
    def reload_rules(self):
        """重新加载规则（支持热重载）"""
        self.load_rules()
        print(f"已重新加载 {len(self.rules)} 条固定回复规则")

    def set_input_area(self, input_area):
        """
        设置输入区域坐标
        :param input_area: 输入区域坐标 (x1, y1, x2, y2)
        """
        self.input_area = input_area
        print(f"已设置输入区域坐标: {input_area}")
    
    def check_message_match(self, message, message_type="user_message"):
        """
        检查消息是否匹配规则
        :param message: 消息内容
        :param message_type: 消息类型 ("user_message" 或 "ai_reply")
        :return: 匹配的规则列表
        """
        matched_rules = []
        
        for rule in self.rules:
            # 检查匹配维度
            match_dimensions = rule.get('match_dimensions', [])
            if message_type not in match_dimensions:
                continue
            
            # 检查关键字匹配
            keyword = rule.get('keyword', '')
            if keyword and keyword in message:
                matched_rules.append(rule)
        
        return matched_rules
    
    def execute_rule_actions(self, rule, delay_before_start=0):
        """
        执行规则的动作
        :param rule: 规则对象
        :param delay_before_start: 开始执行前的延时（秒）
        """
        if self.is_processing:
            print("固定回复处理器正在处理中，跳过本次执行")
            return False
        
        # # 在新线程中执行，避免阻塞主线程
        # thread = threading.Thread(
        #     target=self._execute_actions_thread,
        #     args=(rule, delay_before_start),
        #     daemon=True
        # )
        # thread.start()
        self._execute_actions_thread(rule,delay_before_start)
        return True
    
    def _execute_actions_thread(self, rule, delay_before_start):
        """
        在线程中执行动作
        :param rule: 规则对象
        :param delay_before_start: 开始执行前的延时（秒）
        """
        try:
            self.is_processing = True
            
            # 开始前延时
            if delay_before_start > 0:
                time.sleep(delay_before_start)
            
            actions = rule.get('actions', [])
            print(f"开始执行固定回复规则: {rule.get('name', '未命名规则')}")
            
            for i, action in enumerate(actions):
                action_type = action.get('type')
                delay = action.get('delay', 1.0)
                
                print(f"执行动作 {i+1}/{len(actions)}: {action_type}")
                
                if action_type == 'text':
                    self._send_text(action.get('content', ''))
                elif action_type == 'file':
                    self._send_file(action.get('file_path', ''))
                
                # 动作间延时
                if delay > 0:
                    time.sleep(delay)
            
            print(f"固定回复规则执行完成: {rule.get('name', '未命名规则')}")
            
        except Exception as e:
            print(f"执行固定回复规则时出错: {str(e)}")
        finally:
            self.is_processing = False
    
    def _send_text(self, text):
        """
        发送文字消息
        :param text: 要发送的文字内容
        """
        if not text.strip():
            return

        try:
            # 先点击激活输入框
            if self.input_area:
                input_x1, input_y1, input_x2, input_y2 = self.input_area
                # 计算输入区域的中心点
                target_x = input_x1 + (input_x2 - input_x1) // 2
                target_y = input_y1 + (input_y2 - input_y1) // 2

                print(f"点击激活输入框: ({target_x}, {target_y})")
                pyautogui.moveTo(target_x, target_y)
                time.sleep(0.2)

                if platform.system() == 'Darwin':  # macOS
                    pyautogui.click()
                else:  # Windows/Linux
                    mouse = MouseController()
                    mouse.position = (target_x, target_y)
                    mouse.click(Button.left)

                time.sleep(1)  # 等待输入框激活
            else:
                print("警告：未设置输入区域坐标，可能无法正确发送文字")

            # 保存原始剪贴板内容
            original_clipboard = pyperclip.paste()

            # 复制文字到剪贴板
            pyperclip.copy(text)
            time.sleep(0.2)

            # 粘贴文字
            if platform.system() == 'Darwin':  # macOS
                pyautogui.hotkey('command', 'v')
            else:  # Windows/Linux
                pyautogui.hotkey('ctrl', 'v')

            time.sleep(0.5)

            # 发送消息
            pyautogui.press('enter')

            # 恢复原始剪贴板内容
            pyperclip.copy(original_clipboard)

            print(f"已发送文字: {text[:50]}...")

        except Exception as e:
            print(f"发送文字时出错: {str(e)}")
    
    def _send_file(self, file_path):
        """
        发送文件
        :param file_path: 文件路径
        """
        if not file_path.strip():
            return
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"文件不存在: {file_path}")
                return
            
            # 先点击激活输入框
            if self.input_area:
                input_x1, input_y1, input_x2, input_y2 = self.input_area
                # 计算输入区域的中心点
                target_x = input_x1 + (input_x2 - input_x1) // 2
                target_y = input_y1 + (input_y2 - input_y1) // 2

                print(f"点击激活输入框: ({target_x}, {target_y})")
                pyautogui.moveTo(target_x, target_y)
                time.sleep(0.2)

                if platform.system() == 'Darwin':  # macOS
                    pyautogui.click()
                else:  # Windows/Linux
                    mouse = MouseController()
                    mouse.position = (target_x, target_y)
                    mouse.click(Button.left)

                time.sleep(1)  # 等待输入框激活
            else:
                print("警告：未设置输入区域坐标，可能无法正确发送文字")
            
            # 根据平台选择发送方式
            if platform.system() == 'Darwin':  # macOS
                self._send_file_mac(file_path)
            else:  # Windows/Linux
                self._send_file_windows(file_path)
            
            print(f"已发送文件: {file_path}")
            
        except Exception as e:
            print(f"发送文件时出错: {str(e)}")
    
    def _send_file_mac(self, file_path):
        """
        在macOS上发送文件
        :param file_path: 文件路径
        """
        try:
            from utils.mac_send_file import mac_copy_file_to_clipboard_pyobjc
            
            # 复制文件到剪贴板
            mac_copy_file_to_clipboard_pyobjc(file_path)
            time.sleep(0.5)
            
            # 粘贴文件
            pyautogui.hotkey('command', 'v')
            time.sleep(0.5)
            
            # 发送
            pyautogui.press('enter')

            # 恢复空剪贴板内容
            pyperclip.copy("")
            
        except ImportError:
            print("无法导入macOS文件发送模块")
        except Exception as e:
            print(f"macOS文件发送失败: {str(e)}")
    
    def _send_file_windows(self, file_path):
        """
        在Windows上发送文件
        :param file_path: 文件路径
        """
        try:
            from utils.win_send_file import setClipboardFile
            
            # 复制文件到剪贴板
            setClipboardFile(file_path)
            time.sleep(0.5)
            
            # 粘贴文件
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(0.5)
            
            # 发送
            mouse = MouseController()
            keyboard = KeyboardController()
            keyboard.press(Key.enter)
            keyboard.release(Key.enter)
            
            # 恢复空剪贴板内容
            pyperclip.copy("")
        except ImportError:
            print("无法导入Windows文件发送模块")
        except Exception as e:
            print(f"Windows文件发送失败: {str(e)}")
    
    def process_user_message(self, message):
        """
        处理用户消息，检查是否触发固定回复规则
        :param message: 用户消息内容
        :return: 是否触发了规则（如果触发则不需要调用AI）
        """
        matched_rules = self.check_message_match(message, "user_message")
        
        if matched_rules:
            print(f"用户消息触发了 {len(matched_rules)} 条固定回复规则")
            
            for matched_rule in matched_rules:
                self.execute_rule_actions(matched_rule)
            
            return True  # 表示触发了规则，不需要调用AI
        
        return False  # 没有触发规则，继续正常流程
    
    def process_ai_reply(self, message):
        """
        处理AI回复消息，检查是否触发固定回复规则
        :param message: AI回复消息内容
        """
        matched_rules = self.check_message_match(message, "ai_reply")
        
        if matched_rules:
            print(f"AI回复触发了 {len(matched_rules)} 条固定回复规则")
            
            for matched_rule in matched_rules:
                self.execute_rule_actions(matched_rule, delay_before_start=2.0)
    
    def get_rules_summary(self):
        """
        获取规则摘要信息
        :return: 规则摘要字典
        """
        return {
            'total_rules': len(self.rules),
            'enabled_rules': len([r for r in self.rules if r.get('enabled', True)]),
            'user_message_rules': len([r for r in self.rules if 'user_message' in r.get('match_dimensions', [])]),
            'ai_reply_rules': len([r for r in self.rules if 'ai_reply' in r.get('match_dimensions', [])])
        }
    
    def test_rule_execution(self, rule_id):
        """
        测试规则执行（用于调试）
        :param rule_id: 规则ID
        :return: 是否成功执行
        """
        for rule in self.rules:
            if rule.get('id') == rule_id:
                print(f"测试执行规则: {rule.get('name', '未命名规则')}")
                return self.execute_rule_actions(rule)
        
        print(f"未找到规则ID: {rule_id}")
        return False


# 全局实例
_fixed_reply_processor = None


def get_fixed_reply_processor():
    """获取固定回复处理器的全局实例"""
    global _fixed_reply_processor
    if _fixed_reply_processor is None:
        _fixed_reply_processor = FixedReplyProcessor()
    return _fixed_reply_processor


def reload_fixed_reply_rules():
    """重新加载固定回复规则（用于热重载）"""
    processor = get_fixed_reply_processor()
    processor.reload_rules()
    return processor.get_rules_summary()
