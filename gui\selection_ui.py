import tkinter as tk
import pyautogui
import time

class SelectionUI:
    def __init__(self, callback):
        """
        初始化选择区域UI
        :param callback: 选择完成后的回调函数
        """
        self.callback = callback
        self.select_window = None
        self.canvas = None
        self.is_selecting = False
        self.current_rect = None
        self.drawn_rects = []
        self.temp_coords = None
        self.area_index = 0
        self.area_coords = [None, None, None, None, None, None, None]  # 增加唯一标识区
        self.noise_areas = []
        self.area_names = ["1.聊天区", "2.输入区", "3.联系人区", "4.名称区", "5.唯一标识区", "6.未读气泡区", "7.搜索区"]
        self.area_status = ["未选择", "未选择", "未选择", "未选择", "未选择", "未选择", "未选择"]
        self.instruction_text = None
        self.status_text = None
        
    def start_selection(self, root):
        """开始选择区域流程"""
        # 最小化主窗口
        root.iconify()
        time.sleep(0.5)
        
        # 创建全屏选择窗口
        self.select_window = tk.Toplevel(root)
        self.select_window.attributes('-fullscreen', True)
        self.select_window.attributes('-alpha', 0.4)  # 稍微增加透明度以便看清屏幕
        self.select_window.configure(bg="black")
        
        # 创建画布
        self.canvas = tk.Canvas(self.select_window, bg="black")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 创建提示文本
        self.instruction_text = self.canvas.create_text(
            self.select_window.winfo_screenwidth() // 2, 30, 
            text="1.聊天区(按1确认) → 2.输入区(按2确认) → 3.联系人区(按3确认) → 4.名称区(按4确认) → 5.唯一标识区(按5确认) → 6.未读气泡区(按6确认) → 7.搜索区(按7确认) → 8.干扰区(按8确认，可多个) → 按Enter完成",
            fill="white", font=('微软雅黑', 14, 'bold')
        )
        
        # 创建区域状态提示，添加搜索选区状态
        self.status_text = self.canvas.create_text(
            self.select_window.winfo_screenwidth() // 2, 60,
            text=f"1.聊天区: {self.area_status[0]} | 2.输入区: {self.area_status[1]} | 3.联系人区: {self.area_status[2]} | 4.名称区: {self.area_status[3]} | 5.唯一标识区: {self.area_status[4]} | 6.未读气泡区: {self.area_status[5]} | 7.搜索区: {self.area_status[6]} | 8.干扰区: 0个",
            fill="yellow", font=('微软雅黑', 12)
        )
        
        # 绑定事件处理函数
        self.canvas.bind("<ButtonPress-1>", self.on_area_press)
        self.canvas.bind("<B1-Motion>", self.on_area_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_area_release)
        
        # 键盘事件绑定到顶级窗口
        self.select_window.bind("1", lambda e: self.confirm_area(0))
        self.select_window.bind("2", lambda e: self.confirm_area(1))
        self.select_window.bind("3", lambda e: self.confirm_area(2))
        self.select_window.bind("4", lambda e: self.confirm_area(3))  
        self.select_window.bind("5", lambda e: self.confirm_area(4))  # 唯一标识区
        self.select_window.bind("6", lambda e: self.confirm_area(5))  
        self.select_window.bind("7", lambda e: self.confirm_area(6))  
        self.select_window.bind("8", lambda e: self.confirm_noise_area())  # 干扰区
        self.select_window.bind("<Return>", self.finish_selection)
        self.select_window.bind("<Escape>", lambda e: self.cancel_selection(root))
    
    def on_area_press(self, event):
        """鼠标按下开始框选"""
        self.is_selecting = True
        # 使用pyautogui获取全屏绝对坐标
        pos = pyautogui.position()
        self.x1, self.y1 = pos.x, pos.y
        # 创建新的矩形，用不同颜色标识不同区域
        colors = ["red", "green", "blue", "purple", "orange", "cyan", "magenta"]  # 7个区域的颜色
        color = colors[self.area_index] if self.area_index < len(colors) else "yellow"
        self.current_rect = self.canvas.create_rectangle(
            event.x, event.y, event.x, event.y, 
            outline=color, width=3, dash=(5, 5)
        )
        self._canvas_start_x, self._canvas_start_y = event.x, event.y
    
    def on_area_drag(self, event):
        """鼠标拖动调整框选区域大小"""
        if self.is_selecting and self.current_rect:
            self._canvas_end_x, self._canvas_end_y = event.x, event.y
            self.canvas.coords(self.current_rect, 
                              self._canvas_start_x, self._canvas_start_y, 
                              event.x, event.y)
    
    def on_area_release(self, event):
        """鼠标释放完成当前框选"""
        if self.is_selecting:
            self.is_selecting = False
            # 释放时也用pyautogui获取全屏绝对坐标
            pos = pyautogui.position()
            self.x2, self.y2 = pos.x, pos.y
            if self.x1 > self.x2:
                self.x1, self.x2 = self.x2, self.x1
            if self.y1 > self.y2:
                self.y1, self.y2 = self.y2, self.y1
            
            # 保存当前区域的临时坐标
            self.temp_coords = (self.x1, self.y1, self.x2, self.y2)
            
            # 更新状态提示，但此时区域还未确认
            area_name = self.area_names[self.area_index] if self.area_index < len(self.area_names) else "未知区域"
            key_num = self.area_index + 1
            print(f"已框选{area_name}: ({self.x1}, {self.y1}, {self.x2}, {self.y2})，请按{key_num}键确认或重新框选")
            
            # 绘制虚线矩形表示尚未确认
            if self.current_rect:
                self.canvas.itemconfig(self.current_rect, dash=(5, 5))
                
    def confirm_area(self, area_idx):
        """确认特定区域的选择"""
        if area_idx < 0 or area_idx > 6:  # 修改为6，允许索引0-6，共7个区域
            return
            
        if not hasattr(self, 'temp_coords') or not self.temp_coords:
            key_num = area_idx + 1  # 按键就是索引+1
            print(f"请先框选{self.area_names[area_idx]}，然后再按{key_num}键确认")
            return
            
        # 更新区域坐标
        self.area_coords[area_idx] = self.temp_coords
        
        # 修改为实线矩形表示已确认
        if self.current_rect:
            colors = ["red", "green", "blue", "purple", "orange", "cyan", "magenta"]  # 添加品红色作为第7个区域的颜色
            color = colors[area_idx] if area_idx < len(colors) else "yellow"
            self.canvas.itemconfig(self.current_rect, outline=color, dash=())
            self.drawn_rects.append(self.current_rect)
            self.current_rect = None
        
        # 添加文本标签
        x_center = (self.temp_coords[0] + self.temp_coords[2]) / 2
        y_center = (self.temp_coords[1] + self.temp_coords[3]) / 2
        x_screen = x_center - self.select_window.winfo_rootx()
        y_screen = y_center - self.select_window.winfo_rooty()
        
        self.canvas.create_text(
            x_screen, y_screen,
            text=self.area_names[area_idx],
            fill="white", font=('微软雅黑', 12, 'bold')
        )
        
        # 更新状态
        self.area_status[area_idx] = "已选择"
        self.canvas.itemconfig(
            self.status_text,
            text=f"1.聊天区: {self.area_status[0]} | 2.输入区: {self.area_status[1]} | 3.联系人区: {self.area_status[2]} | 4.名称区: {self.area_status[3]} | 5.唯一标识区: {self.area_status[4]} | 6.未读气泡区: {self.area_status[5]} | 7.搜索区: {self.area_status[6]} | 8.干扰区: {len(self.noise_areas)}个"
        )
        
        print(f"已确认{self.area_names[area_idx]}: {self.area_coords[area_idx]}")
        
        # 移动到下一个区域
        if area_idx < 6:  # 修改为6，对应7个区域
            self.area_index = area_idx + 1
        else:
            self.area_index = 6  # 保持在搜索区
        self.temp_coords = None
    
    def confirm_noise_area(self):
        """确认干扰区域"""
        if not hasattr(self, 'temp_coords') or not self.temp_coords:
            print("请先框选干扰区，然后再按8键确认")  # 修改为按8键
            return
            
        # 添加到干扰区域列表
        self.noise_areas.append(self.temp_coords)
        
        # 修改为白色填充矩形表示干扰区域
        if self.current_rect:
            self.canvas.itemconfig(self.current_rect, outline="white", fill="white", stipple="gray50", dash=())
            self.drawn_rects.append(self.current_rect)
            self.current_rect = None
        
        # 添加文本标签
        x_center = (self.temp_coords[0] + self.temp_coords[2]) / 2
        y_center = (self.temp_coords[1] + self.temp_coords[3]) / 2
        x_screen = x_center - self.select_window.winfo_rootx()
        y_screen = y_center - self.select_window.winfo_rooty()
        
        self.canvas.create_text(
            x_screen, y_screen,
            text=f"干扰区{len(self.noise_areas)}",
            fill="black", font=('微软雅黑', 12, 'bold')
        )
        
        # 更新状态
        self.canvas.itemconfig(
            self.status_text,
            text=f"1.聊天区: {self.area_status[0]} | 2.输入区: {self.area_status[1]} | 3.联系人区: {self.area_status[2]} | 4.名称区: {self.area_status[3]} | 5.唯一标识区: {self.area_status[4]} | 6.未读气泡区: {self.area_status[5]} | 7.搜索区: {self.area_status[6]} | 8.干扰区: {len(self.noise_areas)}个"
        )
        
        print(f"已确认干扰区{len(self.noise_areas)}: {self.temp_coords}")
        
        # 清除临时坐标，准备下一个干扰区域
        self.temp_coords = None
    
    def finish_selection(self, event):
        """完成所有区域选择"""
        # 检查是否所有必需区域都已选择
        required_areas = self.area_coords  # 所有区域都是必需的
        if None in required_areas:
            missing = [self.area_names[i] for i, coord in enumerate(required_areas) if coord is None]
            print(f"请先完成所有区域的选择，缺少: {', '.join(missing)}")
            return
        
        # 关闭选择窗口
        self.select_window.destroy()
        
        # 返回选择结果
        result = {
            'chat_area': self.area_coords[0],
            'input_area': self.area_coords[1],
            'contact_area': self.area_coords[2],
            'contact_name_area': self.area_coords[3],
            'unique_id_area': self.area_coords[4],  # 新增唯一标识区
            'unread_bubble_area': self.area_coords[5],
            'search_area': self.area_coords[6],
            'noise_areas': self.noise_areas
        }
        
        # 调用回调函数
        if self.callback:
            self.callback(result)
    
    def cancel_selection(self, root):
        """取消选择"""
        self.select_window.destroy()
        root.deiconify()
        print("已取消选择") 