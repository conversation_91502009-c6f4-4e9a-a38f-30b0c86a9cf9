
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pwd - imported by posixpath (delayed, conditional), subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), http.server (delayed, optional), webbrowser (delayed), netrc (delayed, conditional), getpass (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), tqdm.utils (delayed, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional), setuptools.sandbox (conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), getpass (optional), tqdm.utils (delayed, optional)
missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (conditional), requests_toolbelt._compat (conditional), lxml.html (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pep517 - imported by importlib.metadata (delayed)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), test.support (delayed, conditional, optional)
missing module named org - imported by pickle (optional)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), paddle.distributed.parallel (top-level), paddle.distributed.launch.utils.kv_server (top-level), paddle.distributed.fleet.base.role_maker (top-level), paddle.incubate.distributed.fleet.role_maker (top-level), paddle.distributed.parallel_with_gloo (top-level)
missing module named multiprocessing.Manager - imported by multiprocessing (top-level), paddle.distributed.parallel (top-level), paddle.distributed.fleet.base.role_maker (top-level), paddle.incubate.distributed.fleet.role_maker (top-level), paddle.distributed.parallel_with_gloo (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), langsmith._internal._background_thread (top-level), paddle.dataset.flowers (top-level), skimage.util.apply_parallel (delayed, conditional, optional)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional)
missing module named _typeshed - imported by numpy.random.bit_generator (top-level), pydantic_core._pydantic_core (top-level), pydantic._internal._dataclasses (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), httpx._transports.wsgi (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional), setuptools._distutils.dist (conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named tomllib - imported by setuptools.compat.py310 (conditional), pydantic.mypy (delayed, conditional), pydantic.v1.mypy (delayed, conditional)
missing module named pyimod02_importers - imported by C:\ProgramData\miniconda3\envs\weixin\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\ProgramData\miniconda3\envs\weixin\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named 'tools.naive_sync_bn' - imported by paddleocr.tools.train (top-level)
missing module named 'tools.program' - imported by paddleocr.tools.eval (top-level), paddleocr.tools.export_center (top-level), paddleocr.tools.export_model (top-level), paddleocr.tools.infer_cls (top-level), paddleocr.tools.infer_det (top-level), paddleocr.tools.infer_e2e (top-level), paddleocr.tools.infer_kie (top-level), paddleocr.tools.infer_kie_token_ser (top-level), paddleocr.tools.infer_kie_token_ser_re (top-level), paddleocr.tools.infer_rec (top-level), paddleocr.tools.infer_sr (top-level), paddleocr.tools.infer_table (top-level), paddleocr.tools.train (top-level)
missing module named 'ppocr.utils' - imported by paddleocr.paddleocr (top-level), paddleocr.ppocr.data.imaug.label_ops (top-level), paddleocr.ppocr.data.imaug.pg_process (top-level), paddleocr.ppocr.data.imaug.fce_aug (top-level), paddleocr.ppocr.data.imaug.ct_process (top-level), paddleocr.ppocr.data.imaug.drrg_targets (top-level), paddleocr.ppocr.postprocess.east_postprocess (top-level), paddleocr.ppocr.postprocess.sast_postprocess (delayed, conditional), paddleocr.ppocr.postprocess.fce_postprocess (top-level), paddleocr.ppocr.postprocess.pg_postprocess (top-level), paddleocr.ppocr.postprocess.vqa_token_ser_layoutlm_postprocess (top-level), paddleocr.ppocr.utils.export_model (top-level), paddleocr.ppocr.utils.loggers.wandb_logger (top-level), paddleocr.ppocr.utils.network (top-level), paddleocr.ppocr.utils.save_load (top-level), paddleocr.ppstructure.layout.predict_layout (top-level), paddleocr.ppstructure.predict_system (top-level), paddleocr.ppstructure.recovery.recovery_to_doc (top-level), paddleocr.ppstructure.recovery.recovery_to_markdown (top-level), paddleocr.ppstructure.table.eval_table (top-level), paddleocr.ppstructure.table.predict_structure (top-level), paddleocr.ppstructure.table.predict_table (top-level), paddleocr.tools.eval (top-level), paddleocr.tools.export_center (top-level), paddleocr.tools.export_model (top-level), paddleocr.tools.infer_cls (top-level), paddleocr.tools.infer_det (top-level), paddleocr.tools.infer_e2e (top-level), paddleocr.tools.infer_kie (top-level), paddleocr.tools.infer_kie_token_ser (top-level), paddleocr.tools.infer_kie_token_ser_re (top-level), paddleocr.tools.infer_rec (top-level), paddleocr.tools.infer_sr (top-level), paddleocr.tools.infer_table (top-level), paddleocr.tools.program (top-level), paddleocr.tools.test_hubserving (top-level), paddleocr.tools.train (top-level)
missing module named 'ppocr.metrics' - imported by paddleocr.tools.eval (top-level), paddleocr.tools.train (top-level)
missing module named 'ppocr.postprocess' - imported by paddleocr.ppocr.postprocess.pse_postprocess.pse_postprocess (top-level), paddleocr.ppocr.utils.export_model (top-level), paddleocr.ppstructure.layout.predict_layout (top-level), paddleocr.ppstructure.table.predict_structure (top-level), paddleocr.tools.eval (top-level), paddleocr.tools.export_center (top-level), paddleocr.tools.infer_cls (top-level), paddleocr.tools.infer_det (top-level), paddleocr.tools.infer_e2e (top-level), paddleocr.tools.infer_kie_token_ser (top-level), paddleocr.tools.infer_kie_token_ser_re (top-level), paddleocr.tools.infer_rec (top-level), paddleocr.tools.infer_sr (top-level), paddleocr.tools.infer_table (top-level), paddleocr.tools.train (top-level)
missing module named 'ppocr.optimizer' - imported by paddleocr.tools.train (top-level)
missing module named 'ppocr.losses' - imported by paddleocr.tools.train (top-level)
missing module named 'ppocr.modeling' - imported by paddleocr.ppocr.utils.export_model (top-level), paddleocr.tools.eval (top-level), paddleocr.tools.export_center (top-level), paddleocr.tools.infer_cls (top-level), paddleocr.tools.infer_det (top-level), paddleocr.tools.infer_e2e (top-level), paddleocr.tools.infer_kie (top-level), paddleocr.tools.infer_kie_token_ser (top-level), paddleocr.tools.infer_kie_token_ser_re (top-level), paddleocr.tools.infer_rec (top-level), paddleocr.tools.infer_sr (top-level), paddleocr.tools.infer_table (top-level), paddleocr.tools.train (top-level)
missing module named 'ppocr.data' - imported by paddleocr.ppocr.data (top-level), paddleocr.ppocr.data.imaug.copy_paste (top-level), paddleocr.ppocr.data.imaug.label_ops (top-level), paddleocr.ppstructure.layout.predict_layout (top-level), paddleocr.ppstructure.table.predict_structure (top-level), paddleocr.tools.eval (top-level), paddleocr.tools.export_center (top-level), paddleocr.tools.infer_cls (top-level), paddleocr.tools.infer_det (top-level), paddleocr.tools.infer_e2e (top-level), paddleocr.tools.infer_kie (top-level), paddleocr.tools.infer_kie_token_ser (top-level), paddleocr.tools.infer_kie_token_ser_re (top-level), paddleocr.tools.infer_rec (top-level), paddleocr.tools.infer_sr (top-level), paddleocr.tools.infer_table (top-level), paddleocr.tools.program (top-level), paddleocr.tools.train (top-level)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional)
missing module named setuptools_scm - imported by tqdm.version (optional)
missing module named pandas - imported by openai._extras.pandas_proxy (delayed, conditional, optional), langsmith.evaluation._runner (delayed, conditional, optional), tqdm.std (delayed, optional), langsmith.evaluation._arunner (conditional), langsmith.client (delayed, conditional), networkx.convert (delayed, optional), networkx.convert_matrix (delayed), networkx.algorithms.centrality.group (delayed), paddle.distributed.auto_tuner.recorder (top-level)
missing module named 'pandas.core' - imported by tqdm.std (delayed, optional)
missing module named 'matplotlib.pyplot' - imported by tqdm.gui (delayed), scipy.spatial._plotutils (delayed), scipy.stats._distribution_infrastructure (delayed, optional), scipy.stats._fit (delayed, conditional), scipy.stats._survival (delayed, conditional), networkx.drawing.nx_pylab (delayed), paddle.dataset.uci_housing (delayed), skimage.io._plugins.matplotlib_plugin (delayed)
missing module named matplotlib - imported by tqdm.gui (delayed), scipy.stats._fit (delayed, optional), scipy.stats._survival (delayed, optional), networkx.drawing.nx_pylab (delayed), paddle.dataset.uci_housing (delayed), skimage._shared._geometry (delayed), tifffile.tifffile (delayed, conditional, optional), skimage.filters.thresholding (delayed), imageio.plugins._tifffile (delayed, conditional, optional)
missing module named trio - imported by httpx._transports.asgi (delayed, conditional), httpcore._synchronization (optional), httpcore._backends.trio (top-level), openai.resources.vector_stores.file_batches (delayed, conditional), tenacity.asyncio (delayed, conditional)
missing module named 'trio.testing' - imported by anyio._backends._trio (delayed)
missing module named apport_python_hook - imported by exceptiongroup._formatting (conditional)
missing module named 'trio.to_thread' - imported by anyio._backends._trio (top-level)
missing module named 'trio.socket' - imported by anyio._backends._trio (top-level)
missing module named outcome - imported by anyio._backends._trio (top-level)
missing module named 'trio.lowlevel' - imported by anyio._backends._trio (top-level)
missing module named 'trio.from_thread' - imported by anyio._backends._trio (top-level)
missing module named _pytest - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by anyio._backends._asyncio (delayed, conditional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named asyncio.Runner - imported by asyncio (conditional), anyio._backends._asyncio (conditional)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.events' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level), urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level), urllib3.http2.connection (top-level)
missing module named h2 - imported by httpcore._sync.http2 (top-level), httpx._client (delayed, conditional, optional)
missing module named 'h2.config' - imported by httpcore._async.http2 (top-level), urllib3.http2.connection (top-level)
missing module named 'rich.table' - imported by httpx._main (top-level)
missing module named 'rich.syntax' - imported by httpx._main (top-level)
missing module named 'rich.progress' - imported by httpx._main (top-level)
missing module named 'rich.markup' - imported by httpx._main (top-level)
missing module named 'rich.console' - imported by httpx._main (top-level)
missing module named 'pygments.util' - imported by httpx._main (top-level), markdown.extensions.codehilite (optional)
missing module named 'pygments.lexers' - imported by Cython.Compiler.Annotate (delayed, optional), httpx._main (top-level), Cython.Debugger.libcython (optional), markdown.extensions.codehilite (optional)
missing module named click - imported by httpx._main (top-level)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named brotlicffi - imported by httpx._decoders (optional), urllib3.util.request (optional), urllib3.response (optional)
missing module named brotli - imported by httpx._decoders (optional), urllib3.util.request (optional), urllib3.response (optional)
missing module named paddle.utils.gast.unparser - imported by paddle.utils.gast.gast (delayed)
missing module named paddle.utils.gast.ast2 - imported by paddle.utils.gast.gast (conditional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named torch - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.torch (top-level), scipy._lib.array_api_compat.torch._info (top-level), scipy._lib.array_api_compat.torch._aliases (top-level), scipy._lib._array_api (delayed, conditional), opt_einsum.backends.torch (delayed, conditional), albumentations.pytorch.transforms (top-level), albumentations.core.utils (delayed, conditional, optional)
missing module named theano - imported by opt_einsum.backends.theano (delayed)
missing module named tensorflow - imported by opt_einsum.backends.tensorflow (delayed, conditional)
missing module named jax - imported by scipy._lib.array_api_compat.common._helpers (delayed), scipy._lib._array_api (delayed, conditional), opt_einsum.backends.jax (delayed, conditional)
missing module named cupy - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib._array_api (delayed, conditional), opt_einsum.backends.cupy (delayed)
missing module named 'paddle.fluid.transpiler' - imported by paddle.distributed.fleet.meta_optimizers.sharding_optimizer (delayed, conditional)
missing module named paddle.fluid - imported by paddle (delayed), paddle.distributed.fleet.meta_optimizers.sharding_optimizer (delayed)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional), paddle.distributed.fleet.base.distributed_strategy (delayed, conditional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named mpi4py - imported by paddle.incubate.distributed.fleet.role_maker (delayed)
missing module named StringIO - imported by six (conditional)
missing module named etcd3 - imported by paddle.distributed.fleet.elastic (delayed), paddle.distributed.launch.controllers.master (delayed), paddle.distributed.launch.utils.etcd_client (top-level)
missing module named python - imported by paddle.amp.grad_scaler (conditional)
missing module named xlsxwriter - imported by paddle.amp.accuracy_compare (delayed, optional)
missing module named graphviz - imported by paddle.jit.sot.utils.info_collector (delayed, optional)
missing module named pydot - imported by networkx.drawing.nx_pydot (delayed)
missing module named pygraphviz - imported by langchain_core.runnables.graph_png (delayed, optional), networkx.drawing.nx_agraph (delayed, optional)
missing module named 'matplotlib.cm' - imported by networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.colors' - imported by networkx.drawing.nx_pylab (delayed), skimage.feature.util (delayed)
missing module named 'matplotlib.patches' - imported by networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.path' - imported by networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.collections' - imported by scipy.spatial._plotutils (delayed), networkx.drawing.nx_pylab (delayed)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional), langsmith.env._runtime_env (optional), scipy._lib._testutils (delayed, optional)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed)
missing module named cupy_backends - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'jax.experimental' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'jax.numpy' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), skimage.util.apply_parallel (delayed, optional)
missing module named sparse - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named dask - imported by scipy._lib.array_api_compat.common._helpers (delayed), skimage.restoration._cycle_spin (optional)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named scipy.special.elliprg - imported by scipy.special (top-level), skimage.draw.draw3d (top-level)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.sparse.csc_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level), scipy.optimize._linprog_highs (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.linalg._sketches (top-level)
missing module named scipy.sparse.linalg.LinearOperator - imported by scipy.sparse.linalg (top-level), scipy.optimize._optimize (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._differentiable_functions (top-level), scipy.optimize._trustregion_constr.minimize_trustregion_constr (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.tr_interior_point (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.linalg.interpolative (delayed), scipy.sparse.csgraph._laplacian (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named 'scikits.umfpack' - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.linalg.splu - imported by scipy.sparse.linalg (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.sparse.linalg.aslinearoperator - imported by scipy.sparse.linalg (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.linalg.interpolative (delayed), scipy.sparse.linalg._svdp (top-level), scipy.sparse.linalg._expm_multiply (top-level), scipy.sparse.linalg._onenormest (top-level)
missing module named scipy.sparse.linalg.lsmr - imported by scipy.sparse.linalg (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._lsq.trf_linear (top-level)
missing module named scipy.sparse.linalg.onenormest - imported by scipy.sparse.linalg (top-level), scipy.linalg._matfuncs_inv_ssq (top-level)
missing module named scipy.sparse.diags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.optimize._milp (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.vstack - imported by scipy.sparse (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.bmat - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.find - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.common (top-level)
missing module named scipy.sparse.coo_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.common (top-level), scipy.stats._crosstab (top-level)
missing module named scipy.sparse.csr_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._lsq.lsq_linear (top-level)
missing module named scipy.sparse.issparse - imported by scipy.sparse (delayed), scipy._lib._array_api (delayed), scipy.sparse.linalg._interface (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._milp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.sparse.csgraph._validation (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named scikits - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named 'matplotlib.ticker' - imported by scipy.stats._fit (delayed)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named scipy.linalg.orthogonal_procrustes - imported by scipy.linalg (top-level), scipy.spatial._procrustes (top-level)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level), networkx.utils.backends (delayed)
missing module named numpy.VisibleDeprecationWarning - imported by numpy (conditional), scipy._lib._util (conditional)
missing module named numpy.ComplexWarning - imported by numpy (conditional), scipy._lib._util (conditional)
missing module named numpy.AxisError - imported by numpy (conditional), scipy._lib._util (conditional), skimage.color.colorconv (optional), skimage.measure._blur_effect (optional)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named pytest - imported by langsmith.testing._internal (optional), networkx.utils.backends (delayed, conditional, optional), scipy._lib._testutils (delayed), skimage._shared.tester (delayed), skimage.filters.rank.tests.test_rank (top-level), skimage.data._fetchers (delayed, conditional), skimage._shared.testing (top-level)
missing module named cffi - imported by scipy._lib._ccallback (delayed, optional), lmdb.cffi (conditional)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named sympy - imported by networkx.algorithms.polynomials (delayed)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named chardet - imported by requests (optional), bs4.dammit (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named 'html5lib.treebuilders' - imported by bs4.builder._html5lib (top-level), lxml.html._html5builder (top-level), lxml.html.html5parser (top-level)
missing module named 'html5lib.constants' - imported by bs4.builder._html5lib (top-level)
missing module named html5lib - imported by bs4.builder._html5lib (top-level), lxml.html.html5parser (top-level)
missing module named urlparse - imported by requests_toolbelt._compat (conditional), lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named 'cython.cimports' - imported by Cython.Plex.DFA (conditional), lxml.html.diff (optional)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named cssselect - imported by lxml.cssselect (optional)
missing module named proto - imported by paddle.base.trainer_desc (delayed)
missing module named 'paddle.base.libpaddle.pir' - imported by paddle.pir (top-level), paddle.pir.core (top-level), paddle.autograd.backward_utils (top-level), paddle.autograd.ir_backward (top-level), paddle.base.executor (top-level), paddle.decomposition.decomp (top-level), paddle.jit.dy2static.py_layer (top-level), paddle.static.nn.control_flow (top-level), paddle.static.nn.static_pylayer (top-level), paddle.static.input (top-level), paddle.vision.transforms.functional (top-level), paddle.vision.transforms.functional_tensor (top-level)
missing module named pathlib2 - imported by paddle.hapi.hub (delayed, optional)
missing module named collection - imported by paddle.hapi.callbacks (conditional)
missing module named paddle.distributed.Group - imported by paddle.distributed (conditional), paddle.distributed.communication.batch_isend_irecv (conditional)
missing module named ujson - imported by paddle.reader.decorator (delayed, optional)
missing module named paddleaudio - imported by paddle.audio.backends.init_backend (delayed, conditional, optional)
missing module named 'ppstructure.predict_system' - imported by paddleocr.paddleocr (top-level), paddleocr.tools.test_hubserving (top-level)
missing module named 'ppstructure.utility' - imported by paddleocr.ppstructure.layout.predict_layout (top-level), paddleocr.ppstructure.predict_system (top-level), paddleocr.ppstructure.table.eval_table (top-level), paddleocr.ppstructure.table.predict_structure (top-level), paddleocr.ppstructure.table.predict_table (top-level), paddleocr.tools.test_hubserving (top-level)
missing module named 'tools.infer' - imported by paddleocr.paddleocr (top-level), paddleocr.ppocr.data.imaug.copy_paste (top-level), paddleocr.ppstructure.layout.predict_layout (top-level), paddleocr.ppstructure.predict_system (top-level), paddleocr.ppstructure.table.predict_structure (top-level), paddleocr.ppstructure.table.predict_table (top-level), paddleocr.ppstructure.utility (top-level), paddleocr.tools.infer_table (top-level), paddleocr.tools.test_hubserving (top-level)
missing module named 'tools.infer_kie_token_ser' - imported by paddleocr.tools.infer_kie_token_ser_re (top-level)
missing module named openpyxl - imported by paddleocr.ppstructure.table.tablepyxl.style (optional), paddleocr.ppstructure.table.tablepyxl.tablepyxl (delayed)
missing module named premailer - imported by paddleocr.ppstructure.table.tablepyxl.tablepyxl (delayed)
missing module named 'openpyxl.utils' - imported by paddleocr.ppstructure.table.tablepyxl.tablepyxl (delayed)
missing module named 'openpyxl.cell' - imported by paddleocr.ppstructure.table.tablepyxl.tablepyxl (delayed)
missing module named 'tablepyxl.style' - imported by paddleocr.ppstructure.table.tablepyxl.tablepyxl (top-level)
missing module named 'openpyxl.styles' - imported by paddleocr.ppstructure.table.tablepyxl.style (optional)
missing module named shapely.has_z - imported by shapely (delayed), shapely._ragged_array (delayed)
missing module named shapely.has_m - imported by shapely (delayed), shapely._ragged_array (delayed)
missing module named tablepyxl - imported by paddleocr.ppstructure.table.predict_table (delayed)
missing module named 'ppstructure.table' - imported by paddleocr.ppstructure.predict_system (top-level), paddleocr.ppstructure.table.eval_table (top-level), paddleocr.ppstructure.table.matcher (top-level), paddleocr.ppstructure.table.predict_table (top-level)
missing module named auto_log - imported by paddleocr.ppstructure.table.predict_structure (delayed, conditional)
missing module named 'ppstructure.recovery' - imported by paddleocr.paddleocr (top-level), paddleocr.ppstructure.predict_system (delayed, conditional), paddleocr.ppstructure.recovery.recovery_to_doc (top-level)
missing module named 'pdf2docx.converter' - imported by paddleocr.ppstructure.predict_system (delayed, conditional)
missing module named 'ppstructure.kie' - imported by paddleocr.ppstructure.predict_system (delayed, conditional)
missing module named paddleclas - imported by paddleocr.ppstructure.predict_system (delayed, conditional)
missing module named 'ppstructure.layout' - imported by paddleocr.ppstructure.predict_system (top-level)
missing module named picodet_postprocess - imported by paddleocr.ppstructure.layout.predict_layout (top-level)
missing module named encryption - imported by paddleocr.ppocr.utils.export_model (delayed, conditional, optional), paddleocr.ppocr.utils.save_load (optional)
missing module named wandb - imported by paddleocr.ppocr.utils.loggers.wandb_logger (delayed, optional)
missing module named lanms - imported by paddleocr.ppocr.data.imaug.drrg_targets (delayed), paddleocr.ppocr.postprocess.east_postprocess (delayed, optional), paddleocr.ppocr.postprocess.sast_postprocess (delayed, conditional)
missing module named ftfy - imported by paddleocr.ppocr.postprocess.rec_postprocess (delayed)
missing module named tokenizers - imported by paddleocr.ppocr.data.imaug.label_ops (delayed), paddleocr.ppocr.postprocess.rec_postprocess (delayed)
missing module named __builtin__ - imported by lmdb.cffi (optional)
missing module named wand - imported by paddleocr.ppocr.data.imaug.unimernet_aug (delayed)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), openai.resources.beta.realtime.realtime (top-level), langchain_core.language_models.base (top-level), langchain_core.load.dump (top-level), langchain_core.load.serializable (top-level), langchain_core.utils.pydantic (top-level), langchain_core.prompts.base (top-level), langchain_core.runnables.base (top-level), langchain_core.documents.compressor (top-level), langsmith.schemas (optional), langsmith.evaluation.evaluator (optional), langsmith.evaluation.string_evaluator (top-level), langchain_core.prompts.structured (top-level), langchain_core.prompts.string (top-level), langchain_core.prompts.prompt (top-level), langchain_core.runnables.graph (conditional), langchain_core.runnables.fallbacks (top-level), langchain_core.tools.base (top-level), langchain_core.utils.function_calling (top-level), langchain_core.tools.convert (top-level), langchain_core.tools.retriever (top-level), langchain_core.runnables.passthrough (top-level), langchain_core.runnables.configurable (top-level), langchain_core.runnables.branch (top-level), langchain_core.runnables.history (top-level), langchain_core.chat_history (top-level), langchain_core.prompts.few_shot (top-level), langchain_core.example_selectors.length_based (top-level), langchain_core.example_selectors.semantic_similarity (top-level), langchain_core.embeddings.fake (top-level), langchain_core.outputs.chat_result (top-level), langchain_core.outputs.llm_result (top-level), langchain_core.outputs.run_info (top-level), langchain_core.language_models.chat_models (top-level), langchain_openai.chat_models.azure (top-level), langchain_openai.chat_models.base (top-level), langchain_openai.embeddings.base (top-level), langchain_core.output_parsers.openai_functions (top-level), langchain_core.structured_query (top-level), albumentations.core.transforms_interface (top-level), albumentations.core.validation (top-level), albumentations.augmentations.pixel.transforms (top-level)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), pydantic.v1._hypothesis_plugin (optional)
missing module named toml - imported by pydantic.v1.mypy (delayed, conditional, optional)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named dotenv - imported by pydantic.v1.env_settings (delayed, optional)
missing module named hypothesis - imported by pydantic.v1._hypothesis_plugin (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.type_visitor' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named 'rich.pretty' - imported by pydantic._internal._core_utils (delayed)
missing module named rich - imported by pydantic._internal._core_utils (conditional)
missing module named pydantic.validate_arguments - imported by pydantic (top-level), langchain_core.tools.base (top-level)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional)
missing module named 'huggingface_hub.utils' - imported by albumentations.core.hub_mixin (optional)
missing module named huggingface_hub - imported by albumentations.core.hub_mixin (optional)
missing module named skimage.filters.sobel - imported by skimage.filters (delayed), skimage.measure._blur_effect (delayed)
missing module named skimage.transform.integral_image - imported by skimage.transform (top-level), skimage.feature.corner (top-level), skimage.filters.thresholding (top-level), skimage.feature.blob (top-level), skimage.feature.censure (top-level)
missing module named skimage.transform.rescale - imported by skimage.transform (top-level), skimage.feature.sift (top-level)
missing module named skimage.transform.pyramid_gaussian - imported by skimage.transform (top-level), skimage.feature.orb (top-level)
missing module named skimage.color.gray2rgb - imported by skimage.color (top-level), skimage.feature._daisy (top-level), skimage.feature.haar (top-level), skimage.feature.texture (top-level)
missing module named skimage.color.rgba2rgb - imported by skimage.color (delayed, conditional), skimage.exposure.exposure (delayed, conditional)
missing module named skimage.color.rgb2gray - imported by skimage.color (delayed, conditional), skimage.exposure.exposure (delayed, conditional), skimage.measure._blur_effect (top-level)
missing module named skimage.draw.rectangle - imported by skimage.draw (top-level), skimage.feature.haar (top-level)
missing module named 'sklearn.mixture' - imported by skimage.feature._fisher_vector (delayed, optional)
missing module named sklearn - imported by skimage.feature._fisher_vector (delayed, optional)
missing module named skimage.measure.block_reduce - imported by skimage.measure (top-level), skimage.transform._warps (top-level)
missing module named skimage.measure.label - imported by skimage.measure (top-level), skimage.restoration.inpaint (top-level)
missing module named skimage.transform.warp - imported by skimage.transform (top-level), skimage.filters._window (top-level)
missing module named skimage.exposure.histogram - imported by skimage.exposure (top-level), skimage.filters.thresholding (top-level)
missing module named skimage.exposure.is_low_contrast - imported by skimage.exposure (top-level), skimage.io._io (top-level), skimage.io._plugins.matplotlib_plugin (top-level)
missing module named pooch - imported by skimage.data._fetchers (delayed, optional)
missing module named 'matplotlib.widgets' - imported by tifffile.tifffile (delayed)
missing module named defusedxml - imported by langchain_core.output_parsers.xml (delayed, conditional, optional), PIL.Image (optional), tifffile.tifffile (delayed, optional)
missing module named 'zarr.indexing' - imported by tifffile.tifffile (delayed, optional)
missing module named 'zarr.hierarchy' - imported by tifffile.tifffile (delayed, optional)
missing module named zarr - imported by tifffile.tifffile (delayed)
missing module named _imagecodecs - imported by tifffile.tifffile (delayed, conditional, optional)
missing module named imagecodecs - imported by tifffile.tifffile (optional), imageio.plugins._tifffile (delayed, conditional, optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional), tifffile._imagecodecs (delayed, optional)
missing module named SimpleITK - imported by skimage.io._plugins.simpleitk_plugin (optional), imageio.plugins.simpleitk (delayed, optional)
missing module named 'matplotlib.image' - imported by skimage.io._plugins.matplotlib_plugin (delayed)
missing module named mpl_toolkits - imported by skimage.io._plugins.matplotlib_plugin (delayed)
missing module named imread - imported by skimage.io._plugins.imread_plugin (optional)
missing module named itk - imported by imageio.plugins.simpleitk (delayed, optional)
missing module named rawpy - imported by imageio.plugins.rawpy (top-level)
missing module named 'av.codec' - imported by imageio.plugins.pyav (top-level)
missing module named 'av.filter' - imported by imageio.plugins.pyav (top-level)
missing module named av - imported by imageio.plugins.pyav (top-level)
missing module named pillow_heif - imported by imageio.plugins.pillow (delayed, optional)
missing module named 'osgeo.gdal' - imported by imageio.plugins.gdal (delayed, optional)
missing module named 'astropy.io' - imported by imageio.plugins.fits (delayed, optional)
missing module named imageio_ffmpeg - imported by imageio.plugins.ffmpeg (top-level)
missing module named tkFileDialog - imported by imageio.plugins._tifffile (delayed, optional)
missing module named Tkinter - imported by pymsgbox (conditional, optional), mouseinfo (conditional, optional), imageio.plugins._tifffile (delayed, optional)
missing module named tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named imageio.plugins.tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named zstd - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named 'backports.lzma' - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named bsdf_cli - imported by imageio.plugins._bsdf (conditional)
missing module named osgeo - imported by skimage.io._plugins.gdal_plugin (optional)
missing module named astropy - imported by skimage.io._plugins.fits_plugin (optional)
missing module named skimage.metrics.mean_squared_error - imported by skimage.metrics (top-level), skimage.restoration.j_invariant (top-level)
missing module named pywt - imported by skimage.restoration._denoise (delayed, optional)
missing module named numpydoc - imported by skimage._shared.utils (delayed, optional)
missing module named fasttext - imported by paddleocr.ppocr.data.imaug.operators (delayed)
missing module named paddlenlp - imported by paddleocr.ppocr.data.imaug.label_ops (delayed)
missing module named Polygon - imported by paddleocr.ppocr.data.imaug.ct_process (delayed)
missing module named pdf2docx - imported by paddleocr.paddleocr (delayed, conditional)
missing module named ppstructure - imported by paddleocr.paddleocr (top-level)
missing module named tools - imported by paddleocr.paddleocr (top-level)
missing module named ppocr - imported by paddleocr.paddleocr (top-level)
missing module named jedityper - imported by Cython.Tests.TestJediTyper (delayed, optional)
missing module named gdb - imported by Cython.Debugger.Tests.test_libcython_in_gdb (top-level), Cython.Debugger.libcython (top-level), Cython.Debugger.libpython (top-level), Cython.Debugger.Tests.test_libpython_in_gdb (top-level)
missing module named 'pygments.formatters' - imported by Cython.Compiler.Annotate (delayed, optional), Cython.Debugger.libcython (optional), markdown.extensions.codehilite (optional)
missing module named runtests - imported by Cython.Debugger.Tests.TestLibCython (top-level)
missing module named 'coverage.tracer' - imported by Cython.Coverage (optional)
missing module named 'coverage.files' - imported by Cython.Coverage (top-level)
missing module named coverage - imported by Cython.Coverage (top-level)
missing module named pythran - imported by Cython.Build.Dependencies (optional), Cython.Compiler.Pythran (optional)
missing module named Cython.Parser - imported by Cython.Compiler.Main (delayed, conditional, optional)
missing module named pygments - imported by Cython.Compiler.Annotate (delayed, optional), markdown.extensions.codehilite (optional)
missing module named 'IPython.core' - imported by Cython.Build.IpythonMagic (top-level), Cython.Build.Tests.TestIpythonMagic (optional)
missing module named 'IPython.testing' - imported by Cython.Build.Tests.TestIpythonMagic (optional)
missing module named pyximport.test - imported by pyximport (conditional), pyximport.pyxbuild (conditional)
missing module named 'IPython.utils' - imported by Cython.Build.IpythonMagic (top-level)
missing module named 'IPython.paths' - imported by Cython.Build.IpythonMagic (optional)
missing module named IPython - imported by Cython.Build.IpythonMagic (top-level)
missing module named orjson.loads - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.dumps - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.JSONDecodeError - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.Fragment - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_UUID - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_NUMPY - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_DATACLASS - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_NON_STR_KEYS - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named 'langchain.evaluation' - imported by langsmith.evaluation.integrations._langchain (delayed, conditional)
missing module named langchain - imported by langsmith.evaluation.integrations._langchain (conditional), langsmith.env._runtime_env (delayed, optional), langchain_core.globals (delayed, optional)
missing module named 'rapidfuzz.distance._initialize_cpp_sse2' - imported by rapidfuzz.distance._initialize (conditional)
missing module named 'rapidfuzz.distance._initialize_cpp_avx2' - imported by rapidfuzz.distance._initialize (conditional)
missing module named 'rapidfuzz.distance.metrics_cpp_sse2' - imported by rapidfuzz.distance.OSA (conditional), rapidfuzz.distance.DamerauLevenshtein (conditional), rapidfuzz.distance.Hamming (conditional), rapidfuzz.distance.Indel (conditional), rapidfuzz.distance.Jaro (conditional), rapidfuzz.distance.JaroWinkler (conditional), rapidfuzz.distance.LCSseq (conditional), rapidfuzz.distance.Levenshtein (conditional), rapidfuzz.distance.Postfix (conditional), rapidfuzz.distance.Prefix (conditional)
missing module named 'rapidfuzz.utils_cpp_sse2' - imported by rapidfuzz.utils (conditional)
missing module named 'rapidfuzz.utils_cpp_avx2' - imported by rapidfuzz.utils (conditional)
missing module named 'rapidfuzz.process_cpp_sse2' - imported by rapidfuzz.process (conditional)
missing module named 'rapidfuzz.process_cpp_avx2' - imported by rapidfuzz.process (conditional)
missing module named 'rapidfuzz.fuzz_cpp_sse2' - imported by rapidfuzz.fuzz (conditional)
missing module named 'opentelemetry.trace' - imported by langsmith.run_helpers (delayed), langsmith.client (conditional, optional), langsmith._internal.otel._otel_exporter (conditional, optional)
missing module named opentelemetry - imported by langsmith.run_helpers (delayed, conditional, optional), langsmith._internal._background_thread (delayed, optional), langsmith.client (conditional, optional), langsmith._internal.otel._otel_exporter (conditional, optional)
missing module named vcr - imported by langsmith.utils (delayed, optional)
missing module named langchain_anthropic - imported by langsmith.client (delayed, optional)
missing module named 'langchain.smith' - imported by langsmith.client (delayed, optional)
missing module named langsmith_pyo3 - imported by langsmith.client (delayed, conditional, optional)
missing module named 'opentelemetry.sdk' - imported by langsmith._internal.otel._otel_client (conditional, optional), langsmith.client (optional)
missing module named 'opentelemetry.context' - imported by langsmith._internal._background_thread (conditional), langsmith._internal.otel._otel_exporter (conditional, optional)
missing module named 'opentelemetry.exporter' - imported by langsmith._internal.otel._otel_client (conditional, optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named Queue - imported by requests_toolbelt._compat (conditional)
missing module named collections.MutableMapping - imported by collections (conditional), requests_toolbelt._compat (conditional), jsonpatch (optional)
missing module named collections.Mapping - imported by collections (conditional), requests_toolbelt._compat (conditional)
missing module named 'requests.packages.urllib3' - imported by requests_toolbelt._compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named collections.MutableSequence - imported by collections (optional), jsonpatch (optional)
missing module named collections.Sequence - imported by collections (optional), jsonpatch (optional), pyautogui (conditional)
missing module named 'tornado.concurrent' - imported by tenacity.tornadoweb (conditional)
missing module named tornado - imported by tenacity (optional), tenacity.tornadoweb (top-level)
missing module named pyppeteer - imported by langchain_core.runnables.graph_mermaid (delayed, optional)
missing module named 'grandalf.routing' - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named 'grandalf.layouts' - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named grandalf - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named jinja2 - imported by langchain_core.prompts.string (delayed, optional)
missing module named langchain_text_splitters - imported by langchain_core.document_loaders.base (delayed, conditional, optional), langchain_core.messages.utils (delayed, conditional, optional)
missing module named transformers - imported by langchain_core.language_models.base (delayed, optional), langchain_openai.embeddings.base (delayed, conditional, optional)
missing module named 'test.test_ttk_textonly' - imported by tkinter.test.test_ttk.test_widgets (top-level)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named 'Xlib.protocol' - imported by pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named 'Xlib.X' - imported by pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named 'Xlib.ext' - imported by pyautogui._pyautogui_x11 (top-level), pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named 'Xlib.display' - imported by pyautogui._pyautogui_x11 (top-level), pynput._util.xorg (top-level), pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named AppKit - imported by pyperclip (delayed, conditional, optional), pyautogui._pyautogui_osx (top-level), pynput.mouse._darwin (top-level)
missing module named Quartz - imported by pygetwindow._pygetwindow_macos (top-level), pyautogui._pyautogui_osx (optional), pynput._util.darwin (top-level), pynput.keyboard._darwin (top-level), pynput.mouse._darwin (top-level)
missing module named 'Xlib.keysymdef' - imported by pynput._util.xorg (top-level), pynput.keyboard._xorg (top-level)
missing module named 'Xlib.XK' - imported by pyautogui._pyautogui_x11 (top-level), pynput._util.xorg (top-level), pynput.keyboard._xorg (top-level)
missing module named 'evdev.events' - imported by pynput.keyboard._uinput (top-level)
missing module named evdev - imported by pynput._util.uinput (top-level), pynput.keyboard._uinput (top-level)
missing module named 'Xlib.threaded' - imported by pynput._util.xorg (top-level)
missing module named CoreFoundation - imported by pynput._util.darwin (top-level)
missing module named HIServices - imported by pynput._util.darwin (top-level)
missing module named objc - imported by pynput._util.darwin (top-level)
runtime module named six.moves - imported by pynput._util (top-level)
missing module named Xlib - imported by mouseinfo (conditional), pyautogui._pyautogui_x11 (top-level)
missing module named 'rubicon.objc' - imported by mouseinfo (conditional)
missing module named rubicon - imported by mouseinfo (conditional)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named 'websockets.exceptions' - imported by openai.resources.beta.realtime.realtime (delayed)
missing module named 'websockets.asyncio' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional)
missing module named 'websockets.sync' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional)
missing module named jiter.from_json - imported by jiter (top-level), openai.lib.streaming.chat._completions (top-level)
missing module named sounddevice - imported by openai._extras.sounddevice_proxy (delayed, conditional, optional)
missing module named 'websockets.extensions' - imported by openai.types.websocket_connection_options (conditional)
missing module named websockets - imported by openai.types.websocket_connection_options (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named Foundation - imported by pyperclip (delayed, conditional, optional)
missing module named PyQt5 - imported by pyperclip (delayed, conditional, optional)
missing module named qtpy - imported by pyperclip (delayed, conditional, optional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
