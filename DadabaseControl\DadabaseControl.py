import sqlite3

DB_NAME = "sqlite3.db"

def create_table():
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS user_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        ai_brain_id TEXT,
        wx_id TEXT
    )
    """)
    conn.commit()
    conn.close()

def insert_user_info(name, ai_brain_id, wx_id):
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute("INSERT INTO user_info (name, ai_brain_id, wx_id) VALUES (?, ?, ?)", (name, ai_brain_id, wx_id))
    conn.commit()
    conn.close()

def get_user_info_by_wx_id(wx_id):
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM user_info WHERE wx_id = ?", (wx_id,)) 
    user_info = cursor.fetchone()
    conn.close()
    return user_info

def get_user_by_name(name):
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM user_info WHERE name = ?", (name,))
    user_info = cursor.fetchone()
    conn.close()
    return user_info

def get_user_by_ai_brain_id(ai_brain_id):
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM user_info WHERE ai_brain_id = ?", (ai_brain_id,))
    user_info = cursor.fetchone()
    conn.close()
    return user_info