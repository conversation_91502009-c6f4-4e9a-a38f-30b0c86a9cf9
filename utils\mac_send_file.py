import os
from Foundation import NSURL
from AppKit import NSPasteboard, NSStringPboardType, NSFilenamesPboardType

def mac_copy_file_to_clipboard_pyobjc(file_path):
    """
    使用 pyobjc 将指定文件复制到 macOS 剪贴板。
    直接将文件URL写入剪贴板，模拟文件拖拽复制。
    """
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return

    # 获取共享剪贴板实例
    pasteboard = NSPasteboard.generalPasteboard()

    # 清除剪贴板现有内容
    pasteboard.clearContents()

    # 将文件路径转换为 NSURL 对象
    # 注意：fileURLWithPath_ 是一个工厂方法，用于从文件路径创建NSURL
    file_url = NSURL.fileURLWithPath_(file_path)

    # 将 NSURL 对象写入剪贴板
    # writeObjects_ 接受一个对象列表，这些对象可以是符合NSPasteboardWriting协议的
    # NSURL 符合这个协议，因此可以直接写入
    if pasteboard.writeObjects_([file_url]):
        print(f"文件 '{file_path}' 已成功通过 pyobjc 复制到剪贴板。")
    else:
        print(f"错误：通过 pyobjc 复制文件 '{file_path}' 到剪贴板失败。")
