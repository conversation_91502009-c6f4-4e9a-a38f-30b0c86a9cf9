import cv2
import numpy as np
import platform
import pyautogui
import mss
import mss.tools
from PIL import Image

def capture_screen_area(x1, y1, x2, y2, noise_areas=None):
    """截取屏幕区域，支持跨平台"""
    try:
        # 截图时严格使用框选坐标
        if platform.system() == 'Darwin':  # macOS
            # 使用 mss 库进行截图以获得清晰图像
            with mss.mss() as sct:
                monitor = {"top": y1, "left": x1, "width": x2 - x1, "height": y2 - y1}
                sct_img = sct.grab(monitor)

                # mss 返回的数据需要先转换为PIL Image，再转换为NumPy数组
                pil_image = Image.frombytes("RGB", sct_img.size, sct_img.rgb)
                cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        else:  # Windows和Linux
            cv_image = np.array(pyautogui.screenshot(region=(x1, y1, x2 - x1, y2 - y1)))
            cv_image = cv2.cvtColor(cv_image, cv2.COLOR_RGB2BGR)
        
        # 处理干扰区域，用白色遮罩覆盖
        if noise_areas:
            for noise_area in noise_areas:
                # 计算相对于主区域的坐标
                nx1, ny1, nx2, ny2 = noise_area
                # 转换为相对于主区域的坐标
                rel_x1 = max(0, nx1 - x1)
                rel_y1 = max(0, ny1 - y1)
                rel_x2 = min(x2 - x1, nx2 - x1)
                rel_y2 = min(y2 - y1, ny2 - y1)
                
                # 判断干扰区是否与主区域有交集
                if rel_x1 < rel_x2 and rel_y1 < rel_y2:
                    # 用白色填充干扰区域
                    cv_image[rel_y1:rel_y2, rel_x1:rel_x2] = [255, 255, 255]  # 白色
        
        return cv_image
    except Exception as e:
        print(f"截图发生错误: {str(e)}")
        # 返回一个空图像
        return np.zeros((10, 10, 3), dtype=np.uint8)

def preprocess_image_mask_green_bubbles(image, green_params):
    """预处理图像，识别并覆盖绿色气泡（助手回复）"""
    try:
        # 解析绿色气泡参数
        green_r = green_params.get('green_r', 80)
        green_g = green_params.get('green_g', 200) 
        green_b = green_params.get('green_b', 80)
        green_tol = green_params.get('green_tol', 40)
        green_min_area = green_params.get('green_min_area', 100)
        debug_mode = green_params.get('debug_mode', False)
        
        # 创建一个图像副本，用于操作
        processed_img = image.copy()
        
        # 使用多种方法检测绿色气泡
        # 方法1: 基于RGB/BGR范围检测
        r, g, b = green_r, green_g, green_b
        tol = green_tol
        
        # 在OpenCV中是BGR顺序
        lower_green_bgr = np.array([max(0, b-tol), max(0, g-tol), max(0, r-tol)])
        upper_green_bgr = np.array([min(255, b+tol), min(255, g+tol), min(255, r+tol)])
        
        if debug_mode:
            print(f"BGR颜色范围: 下限({lower_green_bgr}), 上限({upper_green_bgr})")
        
        # 创建BGR掩码
        bgr_mask = cv2.inRange(processed_img, lower_green_bgr, upper_green_bgr)
        
        # 方法2: 使用HSV颜色空间检测绿色
        # 转换为HSV颜色空间
        hsv = cv2.cvtColor(processed_img, cv2.COLOR_BGR2HSV)
        
        # 绿色在HSV中的范围：H(60左右)，S高，V适中到高
        # 可以根据实际微信气泡颜色调整
        lower_green_hsv = np.array([40, 40, 40])  # 浅绿色到深绿色
        upper_green_hsv = np.array([90, 255, 255])
        
        if debug_mode:
            print(f"HSV颜色范围: 下限({lower_green_hsv}), 上限({upper_green_hsv})")
        
        # 创建HSV掩码
        hsv_mask = cv2.inRange(hsv, lower_green_hsv, upper_green_hsv)
        
        # 合并两个掩码，任一方法检测到的区域都视为绿色气泡
        combined_mask = cv2.bitwise_or(bgr_mask, hsv_mask)
        
        # 对掩码进行形态学操作
        kernel = np.ones((5, 5), np.uint8)
        dilated_mask = cv2.dilate(combined_mask, kernel, iterations=2)
        
        # 查找轮廓
        contours, _ = cv2.findContours(dilated_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 计算找到的绿色区域数量
        green_areas_count = len(contours)
        if debug_mode:
            print(f"找到{green_areas_count}个可能的绿色气泡区域")
        
        # 应用更宽松的参数如果找不到足够的区域
        if green_areas_count == 0:
            if debug_mode:
                print("未找到足够的绿色气泡，尝试使用更宽松的参数")
                return image, None, None
            
            

        # 在原始图像上填充这些轮廓
        masked_count = 0
        last_mask_center_x = None
        last_mask_center_y = None
        max_y_center = -1  # 跟踪最下方遮罩的中心Y坐标
        
        # 按照y坐标对轮廓进行排序，确保我们能找到最下方的遮罩
        sorted_contours = sorted(contours, key=lambda c: cv2.boundingRect(c)[1] + cv2.boundingRect(c)[3])
        
        for contour in sorted_contours:
            # 计算轮廓的面积，过滤掉太小的区域
            area = cv2.contourArea(contour)
            if area < green_min_area:  # 使用用户设定的最小面积阈值
                continue
                
            # 填充轮廓为白色或背景色 (255, 255, 255)
            cv2.drawContours(processed_img, [contour], 0, (255, 255, 255), -1)  # -1表示填充
            masked_count += 1
            
            # 计算轮廓中心点
            x, y, w, h = cv2.boundingRect(contour)
            center_x = x + w // 2
            center_y = y + h // 2
            
            # 判断是否是最下方的遮罩
            if center_y > max_y_center:
                max_y_center = center_y
                last_mask_center_x = center_x
                last_mask_center_y = center_y
            
            # 为调试目的，记录每个覆盖区域的大小
            if debug_mode:
                print(f"覆盖绿色气泡区域 #{masked_count}: 位置({x}, {y}), 大小({w}x{h}), 面积: {area}, 中心点: ({center_x}, {center_y})")
        
        if debug_mode:
            print(f"共覆盖了{masked_count}个绿色气泡区域")
            if last_mask_center_x is not None and last_mask_center_y is not None:
                print(f"最下方遮罩中心点坐标: ({last_mask_center_x}, {last_mask_center_y})")
        
        # 处理语音logo区域
        processed_img = mask_voice_logo_areas(processed_img, debug_mode)
        
        # 如果在调试模式下，保存处理前后的图像对比
        import os
        if debug_mode:
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            import time
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            original_path = os.path.join(debug_dir, f"original_{timestamp}.png")
            processed_path = os.path.join(debug_dir, f"masked_{timestamp}.png")
            mask_path = os.path.join(debug_dir, f"mask_{timestamp}.png")
            cv2.imwrite(original_path, image, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            cv2.imwrite(processed_path, processed_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            cv2.imwrite(mask_path, dilated_mask, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            print(f"已保存原始图像、掩码和处理后图像: {original_path}, {mask_path}, {processed_path}")
        
        # 仅在实际覆盖了区域时才返回坐标，否则返回None
        if masked_count > 0:
            return processed_img, last_mask_center_x, last_mask_center_y
        else:
            return processed_img, None, None
        
    except Exception as e:
        print(f"处理图像遮盖绿色气泡时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return image, None, None  # 出错时返回原始图像和空坐标 

def mask_voice_logo_areas(image, debug_mode=False):
    """
    检测并遮罩语音logo区域
    :param image: 已经处理过绿色气泡的图像
    :param debug_mode: 是否启用调试模式
    :return: 处理后的图像
    """
    try:
        from utils.voice_read_detector import find_voice_logo_with_nms
        import os
        import time
        
        # 保存临时图像用于检测
        temp_img_path = ""
        if debug_mode:
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            temp_img_path = os.path.join(debug_dir, f"temp_for_voice_logo_{timestamp}.png")
        else:
            temp_img_path = "temp_voice_logo_detection.png"
            
        cv2.imwrite(temp_img_path, image, [cv2.IMWRITE_PNG_COMPRESSION, 0])
        
        # 检测语音logo
        if debug_mode:
            print(f"正在检测语音logo...")
        
        voice_logos = find_voice_logo_with_nms(temp_img_path)
        
        # 如果不是调试模式，删除临时文件
        if not debug_mode and os.path.exists(temp_img_path):
            try:
                os.remove(temp_img_path)
            except:
                pass
        
        if voice_logos:
            if debug_mode:
                print(f"找到 {len(voice_logos)} 个语音logo")
            
            # 处理图像副本
            result_img = image.copy()
            height, width = result_img.shape[:2]
            
            # 从设置中获取遮罩范围
            import json
            mask_range = 20  # 默认值
            try:
                with open('settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    mask_range = settings.get('voice_logo_mask_range', 20)
                    if debug_mode:
                        print(f"从settings.json加载语音logo遮罩范围: {mask_range}")
            except Exception as e:
                if debug_mode:
                    print(f"加载语音logo遮罩范围失败，使用默认值20: {str(e)}")
            
            # 遮罩每个语音logo区域
            for i, (logo_x, logo_y) in enumerate(voice_logos):
                # 计算遮罩区域 (y坐标上下mask_range像素，x不限制)
                mask_y_min = max(0, logo_y - mask_range)
                mask_y_max = min(height, logo_y + mask_range)
                
                # 遮罩整行为白色
                result_img[mask_y_min:mask_y_max, 0:width] = [255, 255, 255]
                
                if debug_mode:
                    print(f"已遮罩语音logo #{i+1}: 位置({logo_x}, {logo_y}), 遮罩区域y范围: {mask_y_min}-{mask_y_max}")
            
            # 保存处理后的图像用于调试
            if debug_mode:
                masked_logo_path = os.path.join(debug_dir, f"masked_voice_logo_{timestamp}.png")
                cv2.imwrite(masked_logo_path, result_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                print(f"已保存语音logo遮罩后的图像: {masked_logo_path}")
            
            return result_img
        else:
            if debug_mode:
                print("未检测到语音logo")
            return image
            
    except Exception as e:
        print(f"处理语音logo遮罩时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return image  # 出错时返回原始图像

def filter_text_by_color(image, color_params):
    """
    过滤图像，只保留特定颜色的文字部分（默认白色），其他颜色变为黑色
    :param image: OpenCV格式的图像
    :param color_params: 颜色参数字典，包含r,g,b值和容差
    :return: 处理后的图像
    """
    try:
        # 解析颜色参数
        text_r = color_params.get('text_color_r', 255)  # 默认白色
        text_g = color_params.get('text_color_g', 255)
        text_b = color_params.get('text_color_b', 255)
        text_tol = color_params.get('text_color_tol', 40)
        debug_mode = color_params.get('debug_mode', False)
        
        if debug_mode:
            print(f"文字颜色过滤参数: R({text_r}), G({text_g}), B({text_b}), 容差({text_tol})")
        
        # 创建一个图像副本，用于操作
        processed_img = image.copy()
        
        # 在OpenCV中是BGR顺序
        lower_color_bgr = np.array([max(0, text_b-text_tol), max(0, text_g-text_tol), max(0, text_r-text_tol)])
        upper_color_bgr = np.array([min(255, text_b+text_tol), min(255, text_g+text_tol), min(255, text_r+text_tol)])
        
        # 创建BGR掩码 - 这会生成一个二值图像，匹配颜色区域为白色(255)，其他区域为黑色(0)
        color_mask = cv2.inRange(processed_img, lower_color_bgr, upper_color_bgr)
        
        # 创建一个全黑图像作为背景
        black_background = np.zeros_like(processed_img)
        
        # 将掩码应用到原始图像，只保留符合颜色范围的像素
        # 第一个参数是源图像，第二个参数是掩码
        filtered_img = cv2.bitwise_and(processed_img, processed_img, mask=color_mask)
        
        # 如果在调试模式下，保存处理前后的图像对比
        if debug_mode:
            import os
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            import time
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            original_path = os.path.join(debug_dir, f"text_color_original_{timestamp}.png")
            filtered_path = os.path.join(debug_dir, f"text_color_filtered_{timestamp}.png")
            mask_path = os.path.join(debug_dir, f"text_color_mask_{timestamp}.png")
            cv2.imwrite(original_path, image, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            cv2.imwrite(filtered_path, filtered_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            cv2.imwrite(mask_path, color_mask, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            print(f"已保存文字颜色过滤前后的图像: {original_path}, {filtered_path}, {mask_path}")
        
        return filtered_img
        
    except Exception as e:
        print(f"文字颜色过滤处理时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return image  # 出错时返回原始图像 