import threading
import time
import tkinter as tk
from tkinter import messagebox
import pyautogui
from pynput.mouse import Listener, Controller

class MouseTakeoverDetector06191:
    def __init__(self, app_controller):
        """
        初始化鼠标接管检测器
        :param app_controller: 应用控制器对象，用于与主程序交互
        """
        self.controller = app_controller
        self.monitoring_active06191 = False
        self.detector_thread06191 = None
        self.thread_lock = threading.Lock()  # 添加线程锁
        self.dialog_active = False  # 标记对话框是否激活
        
        # 创建鼠标监听器
        self.mouse_listener06191 = None

    def start_monitoring06191(self):
        return
        """开始监控鼠标接管"""
        with self.thread_lock:
            if self.monitoring_active06191:
                self.controller.debug_log("鼠标接管检测已经在运行中")
                return
                
            # 重置状态
            self.monitoring_active06191 = True
            self.dialog_active = False
            
            # 启动检测线程
            if self.detector_thread06191 is not None and self.detector_thread06191.is_alive():
                self.controller.debug_log("检测到旧的鼠标检测线程仍在运行，等待其退出")
                self.detector_thread06191.join(timeout=2.0)  # 等待旧线程退出，最多2秒
                if self.detector_thread06191.is_alive():
                    self.controller.log("旧的鼠标检测线程未能正常退出，将忽略并创建新线程")
            
            # 创建新的检测线程
            self.detector_thread06191 = threading.Thread(
                target=self.monitor_mouse_movements06191, 
                name="Mouse_Takeover_Detector_Thread"
            )
            self.detector_thread06191.daemon = True
            self.detector_thread06191.start()
            
            self.controller.log("鼠标接管检测已启动")
        
    def stop_monitoring06191(self):
        """停止监控鼠标接管"""
        with self.thread_lock:
            if not self.monitoring_active06191:
                return True
                
            # 设置停止标志
            self.monitoring_active06191 = False
            self.controller.debug_log("鼠标接管检测停止中...")
            
            # 停止鼠标监听器
            if self.mouse_listener06191:
                try:
                    self.mouse_listener06191.stop()
                    self.controller.debug_log("鼠标监听器已停止")
                except Exception as e:
                    self.controller.debug_log(f"停止鼠标监听器时出错: {str(e)}")
                finally:
                    self.mouse_listener06191 = None
            
            # 等待检测线程结束
            if self.detector_thread06191 and self.detector_thread06191.is_alive():
                self.detector_thread06191.join(timeout=2.0)  # 最多等待2秒
                if self.detector_thread06191.is_alive():
                    self.controller.log("警告: 鼠标检测线程未能在2秒内退出")
                else:
                    self.controller.debug_log("鼠标检测线程已正常退出")
            
            self.controller.log("鼠标接管检测已停止")
            return True
    
    def monitor_mouse_movements06191(self):
        """监控鼠标移动并检测真实的用户操作"""
        thread_id = threading.get_ident()
        self.controller.debug_log(f"鼠标检测线程启动: ID {thread_id}")
        
        try:
            # 开始监听鼠标移动，传递injected参数以区分真实和模拟事件
            self.mouse_listener06191 = Listener(
                on_move=lambda x, y, injected=False: self.on_mouse_move06191(x, y, injected),
                on_click=lambda x, y, button, pressed, injected=False: self.on_mouse_click06191(x, y, injected)
            )
            self.mouse_listener06191.start()
            
            while self.monitoring_active06191:
                time.sleep(0.5)  # 每0.5秒检查一次
                
            self.controller.debug_log(f"鼠标检测线程正常退出: ID {thread_id}")
        except Exception as e:
            self.controller.log(f"鼠标检测线程异常: {str(e)}")
            import traceback
            self.controller.debug_log(traceback.format_exc())
    
    def on_mouse_move06191(self, x, y, injected=False):
        """处理鼠标移动事件，检测真实的用户操作"""
        if not self.monitoring_active06191 or self.dialog_active:
            return
            
        # 只有当事件不是由软件注入的(injected=False)，才认为是真实的用户操作
        if not injected:
            self.controller.debug_log(f"检测到真实鼠标操作：坐标 ({x}, {y})")
            self.show_takeover_dialog06191()
    
    def on_mouse_click06191(self, x, y, injected=False):
        """处理鼠标点击事件，检测真实的用户操作"""
        if not self.monitoring_active06191 or self.dialog_active:
            return
            
        # 只有当事件不是由软件注入的(injected=False)，才认为是真实的用户操作
        if not injected:
            self.controller.debug_log(f"检测到真实鼠标点击：坐标 ({x}, {y})")
            self.show_takeover_dialog06191()
    
    def show_takeover_dialog06191(self):
        """显示人为接管确认对话框"""
        # 避免重复显示对话框
        with self.thread_lock:
            if self.dialog_active:
                return
            self.dialog_active = True
        
        # 暂停监控（通过触发停止监控按钮）
        was_monitoring = self.controller.monitoring
        if was_monitoring:
            # 如果正在回复，需要中断回复过程
            if hasattr(self.controller, 'is_replying') and self.controller.is_replying:
                self.controller.is_replying = False
                self.controller.log("检测到人为接管，已中断正在进行的回复")
            
            # 停止监控
            self.controller.debug_log("检测到人为接管，暂停监控")
            self.controller.stop_monitoring()

        # 播放警告声音
        # 播放警告声音（叮咚类似的系统声音）
        try:
            import platform
            
            # 根据操作系统选择不同的声音播放方式
            if platform.system() == 'Windows':
                import winsound
                # Windows系统使用winsound播放系统声音
                winsound.PlaySound("SystemExclamation", winsound.SND_ALIAS)
                self.controller.debug_log("已播放Windows系统警告声音")
            elif platform.system() == 'Darwin':  # macOS
                # macOS系统使用系统命令播放声音
                import os
                os.system("afplay /System/Library/Sounds/Tink.aiff")
                self.controller.debug_log("已播放macOS系统警告声音")
            else:
                # Linux或其他系统，尝试使用beep命令
                import os
                try:
                    os.system("beep")
                    self.controller.debug_log("已播放系统beep声音")
                except:
                    self.controller.debug_log("无法播放系统声音，当前平台不支持")
        except Exception as e:
            self.controller.debug_log(f"播放警告声音时出错: {str(e)}")
        
        # 在主线程中显示对话框，避免线程问题
        if hasattr(self.controller, 'root') and self.controller.root:
            self.controller.root.after(0, lambda: self._show_dialog_in_main_thread06191(was_monitoring))
        else:
            self.controller.log("无法显示接管对话框：未找到主窗口")
            # 重置对话框状态
            self.dialog_active = False

    
    def _show_dialog_in_main_thread06191(self, was_monitoring):
        """在主线程中显示对话框"""
        try:
            # 创建一个Toplevel窗口来承载对话框，使其始终显示在最前面
            from tkinter import Toplevel
            
            # 创建Toplevel窗口
            dialog_window = Toplevel(self.controller.root)
            dialog_window.withdraw()  # 先隐藏窗口
            
            # 设置窗口属性为总是在最前
            dialog_window.attributes('-topmost', True)
            
            # 显示确认对话框
            dialog_window.deiconify()  # 显示窗口
            response = messagebox.askquestion("检测到人为操作", 
                                            "您已接管此电脑，请问是否需要暂停自动监控？",
                                            icon='question',
                                            parent=dialog_window)
            
            # 关闭Toplevel窗口
            dialog_window.destroy()
            
            # 处理用户的选择
            if response == 'yes':
                # 用户选择"是"，保持暂停状态，不需要额外操作
                self.controller.log("用户选择保持暂停状态")
                # 确保stop_requested06141设置为True
                if hasattr(self.controller, 'stop_requested06141'):
                    self.controller.stop_requested06141 = True
            else:
                # 用户选择"否"，恢复监控
                self.controller.log("用户选择恢复监控")
                if was_monitoring:
                    # 确保重置stop_requested06141标志
                    if hasattr(self.controller, 'stop_requested06141'):
                        self.controller.stop_requested06141 = False
                        
                    # 重新启动监控
                    self.controller.debug_log("用户请求恢复监控，重新启动监控")
                    self.controller.start_monitoring()
            
            # 重置对话框状态
            self.dialog_active = False
                    
        except Exception as e:
            self.controller.log(f"显示对话框时出错: {str(e)}")
            import traceback
            self.controller.debug_log(traceback.format_exc())
            # 重置对话框状态
            self.dialog_active = False 