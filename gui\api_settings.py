import tkinter as tk
from tkinter import ttk, messagebox
import json
import os

class APISettingsPage:
    def __init__(self, master, settings_file="settings.json"):
        """
        API设置页面
        :param master: 父窗口
        :param settings_file: 设置文件路径
        """
        self.master = master
        self.settings_file = settings_file
        self.settings = self.load_settings()
        
        self.create_widgets()
    
    def load_settings(self):
        """加载设置"""
        if os.path.exists(self.settings_file):
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {
            "provider": "openai",
            "api_key": "",
            "api_base_url": "https://api.openai.com",
            "model_name": "gpt-3.5-turbo",
            "bot_id": "",
            "token": "",
            "system_prompt": "你是一个有用的助手。请用简短、友好的方式回答问题。",
            "delay": 0.1,
            "debug_mode": False
        }
    
    def save_settings(self):
        """保存设置"""
        with open(self.settings_file, 'w', encoding='utf-8') as f:
            json.dump(self.settings, f, ensure_ascii=False, indent=2)
    
    def create_widgets(self):
        """创建界面元素"""
        # 创建主框架
        main_frame = ttk.Frame(self.master, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 服务提供商选择
        provider_frame = ttk.LabelFrame(main_frame, text="选择服务提供商", padding=10)
        provider_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 使用 RadioButton 实现互斥选择
        self.provider_var = tk.StringVar(value=self.settings.get("provider", "openai"))
        
        # OpenAI 选项
        self.openai_radio = ttk.Radiobutton(
            provider_frame, 
            text="OpenAI", 
            variable=self.provider_var, 
            value="openai",
            command=self.update_provider_state
        )
        self.openai_radio.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Coze Space 选项
        self.coze_radio = ttk.Radiobutton(
            provider_frame, 
            text="Coze Space", 
            variable=self.provider_var, 
            value="coze",
            command=self.update_provider_state
        )
        self.coze_radio.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # OpenAI 设置区域
        self.openai_frame = ttk.LabelFrame(main_frame, text="OpenAI 设置", padding=10)
        self.openai_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # OpenAI API URL
        ttk.Label(self.openai_frame, text="API URL:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_url_var = tk.StringVar(value=self.settings.get("api_base_url", "https://api.openai.com"))
        self.api_url_entry = ttk.Entry(self.openai_frame, textvariable=self.api_url_var, width=40)
        self.api_url_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # OpenAI API Key
        ttk.Label(self.openai_frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_key_var = tk.StringVar(value=self.settings.get("api_key", ""))
        self.api_key_entry = ttk.Entry(self.openai_frame, textvariable=self.api_key_var, width=40, show="*")
        self.api_key_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 显示/隐藏 API Key
        self.show_key_var = tk.BooleanVar(value=False)
        self.show_key_check = ttk.Checkbutton(
            self.openai_frame, 
            text="显示API Key", 
            variable=self.show_key_var,
            command=lambda: self.api_key_entry.config(show="" if self.show_key_var.get() else "*")
        )
        self.show_key_check.grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 模型名称
        ttk.Label(self.openai_frame, text="模型名称:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.model_name_var = tk.StringVar(value=self.settings.get("model_name", "gpt-3.5-turbo"))
        self.model_name_combo = ttk.Combobox(
            self.openai_frame, 
            textvariable=self.model_name_var,
            values=["gpt-3.5-turbo", "gpt-4", "gpt-4o", "claude-3-opus-20240229", "claude-3-sonnet-20240229"]
        )
        self.model_name_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Coze Space 设置区域
        self.coze_frame = ttk.LabelFrame(main_frame, text="Coze Space 设置", padding=10)
        self.coze_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Bot ID
        ttk.Label(self.coze_frame, text="Bot ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.bot_id_var = tk.StringVar(value=self.settings.get("bot_id", ""))
        self.bot_id_entry = ttk.Entry(self.coze_frame, textvariable=self.bot_id_var, width=40)
        self.bot_id_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Token
        ttk.Label(self.coze_frame, text="Token:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.token_var = tk.StringVar(value=self.settings.get("token", ""))
        self.token_entry = ttk.Entry(self.coze_frame, textvariable=self.token_var, width=40, show="*")
        self.token_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 显示/隐藏 Token
        self.show_token_var = tk.BooleanVar(value=False)
        self.show_token_check = ttk.Checkbutton(
            self.coze_frame, 
            text="显示Token", 
            variable=self.show_token_var,
            command=lambda: self.token_entry.config(show="" if self.show_token_var.get() else "*")
        )
        self.show_token_check.grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 调试模式设置
        debug_frame = ttk.Frame(main_frame)
        debug_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 调试模式
        self.debug_mode_var = tk.BooleanVar(value=self.settings.get("debug_mode", False))
        self.debug_mode_check = ttk.Checkbutton(
            debug_frame, 
            text="调试模式", 
            variable=self.debug_mode_var
        )
        self.debug_mode_check.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 保存按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        self.save_button = ttk.Button(
            button_frame, 
            text="保存设置",
            command=self.save
        )
        self.save_button.pack(side=tk.RIGHT, padx=5)
        
        # 初始化界面状态
        self.update_provider_state()
    
    def update_provider_state(self):
        """更新提供商选择状态，启用/禁用相应的设置项"""
        provider = self.provider_var.get()
        
        if provider == "openai":
            # 启用OpenAI设置，禁用Coze设置
            for child in self.openai_frame.winfo_children():
                if isinstance(child, (ttk.Entry, ttk.Combobox, ttk.Checkbutton)):
                    child.configure(state="normal")
            for child in self.coze_frame.winfo_children():
                if isinstance(child, (ttk.Entry, ttk.Checkbutton)):
                    child.configure(state="disabled")
        elif provider == "coze":
            # 启用Coze设置，禁用OpenAI设置
            for child in self.openai_frame.winfo_children():
                if isinstance(child, (ttk.Entry, ttk.Combobox, ttk.Checkbutton)):
                    child.configure(state="disabled")
            for child in self.coze_frame.winfo_children():
                if isinstance(child, (ttk.Entry, ttk.Checkbutton)):
                    child.configure(state="normal")
    
    def save(self):
        """保存设置到文件"""
        provider = self.provider_var.get()
        
        # 更新设置
        self.settings["provider"] = provider
        self.settings["api_base_url"] = self.api_url_var.get()
        self.settings["api_key"] = self.api_key_var.get()
        self.settings["model_name"] = self.model_name_var.get()
        self.settings["bot_id"] = self.bot_id_var.get()
        self.settings["token"] = self.token_var.get()
        self.settings["system_prompt"] = "你是一个有用的助手。请用简短、友好的方式回答问题。"  # 使用默认值
        self.settings["delay"] = 0.1  # 使用默认值
        self.settings["debug_mode"] = self.debug_mode_var.get()
        
        # 保存到文件
        self.save_settings()
        
        # 提示保存成功
        messagebox.showinfo("成功", "设置已保存")

# 用于测试的代码
if __name__ == "__main__":
    root = tk.Tk()
    root.title("API设置")
    root.geometry("600x500")
    app = APISettingsPage(root)
    root.mainloop() 