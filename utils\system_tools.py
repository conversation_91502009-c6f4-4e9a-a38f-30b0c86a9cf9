import datetime
import json
import os
import rsa

import platform
import subprocess
import hashlib

def get_mac_address():
    """获取 MAC 地址 (可能返回多个，选择第一个非零地址)。"""
    try:
        if platform.system() == "Windows":
            output = subprocess.check_output("getmac /v /fo list", shell=True, text=True)
            for line in output.splitlines():
                if "物理地址" in line:
                    mac = line.split(":")[-1].strip().replace("-", "").lower()
                    if mac != "000000000000":
                        return mac
        elif platform.system() == "Linux":
            output = subprocess.check_output(["ip", "link"], text=True)
            for line in output.splitlines():
                if "ether" in line:
                    mac = line.split("ether")[1].strip().split()[0].lower()
                    if mac != "00:00:00:00:00:00":
                        return mac.replace(":", "")
        elif platform.system() == "Darwin":  # macOS
            output = subprocess.check_output(["ifconfig", "en0"], text=True)
            for line in output.splitlines():
                if "ether" in line:
                    mac = line.split("ether")[1].strip().split()[0].lower()
                    return mac.replace(":", "")
    except Exception:
        pass
    return None

def get_machine_id():
    """尝试获取机器 ID (不同平台实现不同)。"""
    machine_id = None
    try:
        if platform.system() == "Windows":
            output = subprocess.check_output("wmic csproduct get UUID", shell=True, text=True)
            for line in output.splitlines():
                if "UUID" in line:
                    continue
                uuid_str = line.strip()
                if uuid_str:
                    machine_id = uuid_str.lower()
                    break
        elif platform.system() == "Linux":
            try:
                with open("/etc/machine-id", "r") as f:
                    machine_id = f.readline().strip().lower()
            except FileNotFoundError:
                try:
                    with open("/var/lib/dbus/machine-id", "r") as f:
                        machine_id = f.readline().strip().lower()
                except FileNotFoundError:
                    pass
        elif platform.system() == "Darwin":  # macOS
            output = subprocess.check_output(["ioreg", "-rd1", "-c", "IOPlatformExpertDevice"], text=True)
            for line in output.splitlines():
                if "IOPlatformUUID" in line:
                    machine_id = line.split("=")[1].strip().replace('"', '').lower()
                    break
    except Exception:
        pass
    return machine_id

def get_cpu_info():
    """获取 CPU 信息 (简化)。"""
    return platform.processor()

def get_platform_info():
    """获取操作系统和平台信息。"""
    return platform.system() + " " + platform.release() + " " + platform.machine()

def generate_hardware_id():
    """生成一个基于多种硬件和系统信息的哈希值。"""
    machine_id = get_machine_id()
    cpu_info = get_cpu_info()
    platform_info = get_platform_info()

    combined_info = ""
    if machine_id:
        combined_info += machine_id
    if cpu_info:
        combined_info += cpu_info
    if platform_info:
        combined_info += platform_info

    return hashlib.sha256(combined_info.encode()).hexdigest()



def check_lisence():
    public_key = rsa.PublicKey.load_pkcs1("""-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAjtI3xROGrGoDvSiVBIfPv6Mc3gvCbV7A2nJrPe8VDzYKO/c4yT88
UIHsl9kjDcjR51ZJ5nKkoMf7em1M+JdXfe1ChdMdvL5iOOEBU/ojq4cui3t61/rz
I4ughSkwV1oRz+oSBI1vCEcG4Yflw+/PE8hzv0Ugayq5Mi+LjzMIJLNbHefhXz0M
VE8uuA8eBD+en2XRiuFgxKp4GuQebPD6svT3vses74eicKrEgxdBpXCe5z+JzTa/
d3YDz1onfEz96zagtTgi6Y63Mvn55Ajjt4f3FAdoCMubog//48QdKYzkPrBBSdQc
6f0gtwj/4KEfv1pA9tWR1JWg3bahIMZQ/QIDAQAB
-----END RSA PUBLIC KEY-----
""")
    private_key = rsa.PrivateKey.load_pkcs1("""*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************""")
    # 判断当前目录下有没有gr3后缀的文件，如果有读取，没有则生成机器码
    license_file_name = ""
    for filename in os.listdir('.'):
        if filename.endswith(".gr3"):
            license_file_name = filename
            break
        else:
            pass


    if license_file_name != "":
        try:
            with open(license_file_name, "rb") as f:
                decrypted = rsa.decrypt(f.read(), private_key).decode()
                json_data = json.loads(decrypted)
                if json_data["hid"] ==  generate_hardware_id() and json_data["type"] == "AI视觉聊天助手":
                    start_time = json_data["start_time"]
                    end_time = json_data["end_time"]
                    name = json_data["name"]
                    if start_time * 10000 < int(datetime.datetime.now().timestamp()) * 10000 < end_time * 10000:
                        print(f"🎉欢迎 {name} 使用 视觉监控聊天助手，有效期至{datetime.datetime.fromtimestamp(end_time)}，本软件仅供学习参考使用，若用于非法用途，概不负责。\n")
                        return True
                    else:
                        print("❌授权文件已过期，请和作者联系获取新的有效 gr3 授权文件\n")
                        return False
                else:
                    print("❌无法识别授权文件\n")
        except Exception as e:
            print(f"❌错误:{str(e)}")
            return False
    else:
        hardware_id = "AI视觉聊天助手|" + generate_hardware_id()
        encrypted = rsa.encrypt(hardware_id.encode(), public_key).hex()
        with open('needgr3.txt','w',encoding='utf-8') as w:
            w.write(f"{encrypted}")
            w.close()
        print(f"🎉生成机器码：needgr3.txt\n🎉请联系作者获取授权文件\n")
        return False