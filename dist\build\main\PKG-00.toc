('D:\\mac\\视觉监控工具\\dist\\build\\main\\main.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'D:\\mac\\视觉监控工具\\dist\\build\\main\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'D:\\mac\\视觉监控工具\\dist\\build\\main\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\mac\\视觉监控工具\\dist\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\mac\\视觉监控工具\\dist\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\mac\\视觉监控工具\\dist\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\mac\\视觉监控工具\\dist\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'D:\\mac\\视觉监控工具\\dist\\main.py', 'PYSOURCE'),
  ('gui\\pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'D:\\mac\\视觉监控工具\\dist\\gui\\pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'BINARY'),
  ('monitor\\pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'D:\\mac\\视觉监控工具\\dist\\monitor\\pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'BINARY'),
  ('ocr\\pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'D:\\mac\\视觉监控工具\\dist\\ocr\\pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'BINARY'),
  ('utils\\pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'D:\\mac\\视觉监控工具\\dist\\utils\\pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'BINARY'),
  ('Shapely.libs\\geos-a5c01bdebd805679c9540218609cd4b8.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Shapely.libs\\geos-a5c01bdebd805679c9540218609cd4b8.dll',
   'BINARY'),
  ('Shapely.libs\\msvcp140-8a79f4687fc453279df1092923244d9e.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Shapely.libs\\msvcp140-8a79f4687fc453279df1092923244d9e.dll',
   'BINARY'),
  ('Shapely.libs\\geos_c-f79418bf8e5cda2d1933e2121ee44e49.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Shapely.libs\\geos_c-f79418bf8e5cda2d1933e2121ee44e49.dll',
   'BINARY'),
  ('libiomp5md.dll', 'D:\\mac\\视觉监控工具\\dist\\libiomp5md.dll', 'BINARY'),
  ('mklml.dll', 'D:\\mac\\视觉监控工具\\dist\\mklml.dll', 'BINARY'),
  ('python310.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\python310.dll',
   'BINARY'),
  ('scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('msvcp140.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\msvcp140.dll',
   'BINARY'),
  ('vcruntime140_1.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\vcruntime140_1.dll',
   'BINARY'),
  ('vcruntime140.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\vcruntime140.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\PIL\\_imagingft.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\PIL\\_imagingmath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\zstandard\\_cffi.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\zstandard\\backend_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('google\\_upb\\_message.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\google\\_upb\\_message.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('paddle\\base\\libpaddle.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddle\\base\\libpaddle.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\_lib\\_fpumode.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\_lib\\messagestream.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\cython_lapack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\cython_blas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\_csparsetools.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\sparse\\_sparsetools.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\_decomp_update.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_rank_filter_1d.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\ndimage\\_rank_filter_1d.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\ndimage\\_nd_image.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\ndimage\\_ni_label.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\spatial\\_hausdorff.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_interpnd.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\interpolate\\_interpnd.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_bspl.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\interpolate\\_bspl.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_core.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_highspy\\_core.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_highs_options.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_options.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_direct.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_group_columns.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_pava_pybind.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_lsap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_interpolative.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\_decomp_interpolative.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cython_nnls.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_cython_nnls.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqp.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_slsqp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_zeros.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_minpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cobyla.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_cobyla.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\interpolate\\_ppoly.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dierckx.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\interpolate\\_dierckx.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dfitpack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\interpolate\\_dfitpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\interpolate\\_fitpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\spatial\\_voronoi.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\spatial\\_qhull.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\spatial\\_ckdtree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\stats\\_qmc_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\stats\\_sobol.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\stats\\_stats_pythran.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_mvn.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\stats\\_mvn.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\stats\\_biasedurn.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\stats\\_stats.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\special\\cython_special.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_linalg_pythran.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\_linalg_pythran.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\_flapack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\linalg\\_fblas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_gufuncs.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\special\\_gufuncs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_special_ufuncs.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\special\\_special_ufuncs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\special\\_comb.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\special\\_specfun.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\special\\_ufuncs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\integrate\\_lsoda.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\integrate\\_dop.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\integrate\\_vode.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\integrate\\_quadpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\integrate\\_odepack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\etree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\_elementpath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\sax.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\objectify.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\html\\diff.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\_difflib.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\html\\_difflib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\builder.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\yaml\\_yaml.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('shapely\\lib.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\shapely\\lib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('shapely\\_geometry_helpers.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\shapely\\_geometry_helpers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('shapely\\_geos.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\shapely\\_geos.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyclipper\\_pyclipper.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\pyclipper\\_pyclipper.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lmdb\\cpython.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lmdb\\cpython.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('stringzilla.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\stringzilla.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('simsimd.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\simsimd.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\pydantic_core\\_pydantic_core.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('skimage\\morphology\\_max_tree.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\morphology\\_max_tree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\morphology\\_flood_fill_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\morphology\\_flood_fill_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\morphology\\_extrema_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\morphology\\_extrema_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\morphology\\_misc_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\morphology\\_misc_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\morphology\\_grayreconstruct.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\morphology\\_grayreconstruct.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_peak_finding_utils.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\signal\\_peak_finding_utils.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_sosfilt.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\signal\\_sosfilt.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_spline.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\signal\\_spline.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_upfirdn_apply.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\signal\\_upfirdn_apply.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_max_len_seq_inner.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\signal\\_max_len_seq_inner.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_sigtools.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\signal\\_sigtools.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\orb_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\orb_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\draw\\_draw.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\draw\\_draw.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\corner_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\corner_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\censure_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\censure_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\brief_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\brief_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\_texture.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\_texture.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\_sift.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\_sift.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\_hoghistogram.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\_hoghistogram.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\_hessian_det_appx.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\_hessian_det_appx.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\_haar.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\_haar.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\_cascade.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\_cascade.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\feature\\_canny_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\_canny_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\measure\\_pnpoly.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\measure\\_pnpoly.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\measure\\_moments_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\measure\\_moments_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\measure\\_marching_cubes_lewiner_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\measure\\_marching_cubes_lewiner_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\measure\\_find_contours_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\measure\\_find_contours_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\measure\\_ccomp.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\measure\\_ccomp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\transform\\_warps_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\transform\\_warps_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\transform\\_radon_transform.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\transform\\_radon_transform.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\transform\\_hough_transform.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\transform\\_hough_transform.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\_shared\\transform.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\_shared\\transform.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\_shared\\geometry.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\_shared\\geometry.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\filters\\rank\\percentile_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\filters\\rank\\percentile_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\filters\\rank\\generic_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\filters\\rank\\generic_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\filters\\rank\\core_cy_3d.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\filters\\rank\\core_cy_3d.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\filters\\rank\\core_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\filters\\rank\\core_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\filters\\rank\\bilateral_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\filters\\rank\\bilateral_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\restoration\\_unwrap_3d.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\restoration\\_unwrap_3d.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\restoration\\_unwrap_2d.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\restoration\\_unwrap_2d.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\restoration\\_unwrap_1d.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\restoration\\_unwrap_1d.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\restoration\\_rolling_ball_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\restoration\\_rolling_ball_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\restoration\\_nl_means_denoising.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\restoration\\_nl_means_denoising.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\restoration\\_inpaint.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\restoration\\_inpaint.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\restoration\\_denoise_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\restoration\\_denoise_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\filters\\_multiotsu.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\filters\\_multiotsu.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\morphology\\_convex_hull.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\morphology\\_convex_hull.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\morphology\\_skeletonize_various_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\morphology\\_skeletonize_various_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\morphology\\_skeletonize_lee_cy.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\morphology\\_skeletonize_lee_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\util\\_remap.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\util\\_remap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Utils.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utils.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Tempita\\_tempita.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tempita\\_tempita.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\StringIOTree.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\StringIOTree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Runtime\\refnanny.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Runtime\\refnanny.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Plex\\Transitions.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Transitions.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Plex\\Scanners.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Scanners.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Plex\\Machines.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Machines.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Plex\\DFA.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\DFA.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Plex\\Actions.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Actions.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Compiler\\Visitor.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Visitor.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Compiler\\Scanning.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Scanning.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Compiler\\Parsing.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Parsing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Compiler\\LineTable.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\LineTable.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Compiler\\FusedNode.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\FusedNode.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Compiler\\FlowControl.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\FlowControl.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('Cython\\Compiler\\Code.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Code.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('orjson\\orjson.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\orjson\\orjson.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\distance\\_initialize_cpp.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\rapidfuzz\\distance\\_initialize_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\_feature_detector_cpp.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\rapidfuzz\\_feature_detector_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\distance\\metrics_cpp.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\rapidfuzz\\distance\\metrics_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\distance\\metrics_cpp_avx2.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\rapidfuzz\\distance\\metrics_cpp_avx2.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\utils_cpp.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\rapidfuzz\\utils_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\process_cpp_impl.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\rapidfuzz\\process_cpp_impl.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\fuzz_cpp.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\rapidfuzz\\fuzz_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\fuzz_cpp_avx2.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\rapidfuzz\\fuzz_cpp_avx2.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_testinternalcapi.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_testinternalcapi.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('regex\\_regex.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\regex\\_regex.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('tiktoken\\_tiktoken.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\tiktoken\\_tiktoken.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('jiter\\jiter.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\jiter\\jiter.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32clipboard.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\win32\\win32clipboard.pyd',
   'EXTENSION'),
  ('Levenshtein\\levenshtein_cpp.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Levenshtein\\levenshtein_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'D:\\mac\\视觉监控工具\\dist\\pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'C:\\ProgramData\\miniconda3\\envs\\weixin\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('libssl-1_1-x64.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('ffi.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\ffi.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\python3.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes310.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\pywin32_system32\\pywintypes310.dll',
   'BINARY'),
  ('paddle\\libs\\common.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddle\\libs\\common.dll',
   'BINARY'),
  ('paddle\\libs\\libiomp5md.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddle\\libs\\libiomp5md.dll',
   'BINARY'),
  ('paddle\\libs\\mkldnn.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddle\\libs\\mkldnn.dll',
   'BINARY'),
  ('scipy\\special\\libsf_error_state.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\scipy\\special\\libsf_error_state.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\tcl86t.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\tk86t.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\ucrtbase.dll',
   'BINARY'),
  ('VCOMP140.DLL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\VCOMP140.DLL',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('Cython\\Build\\BuildExecutable.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\BuildExecutable.py',
   'DATA'),
  ('Cython\\Build\\Cache.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Cache.py',
   'DATA'),
  ('Cython\\Build\\Cythonize.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Cythonize.py',
   'DATA'),
  ('Cython\\Build\\Dependencies.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Dependencies.py',
   'DATA'),
  ('Cython\\Build\\Distutils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Distutils.py',
   'DATA'),
  ('Cython\\Build\\Inline.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Inline.py',
   'DATA'),
  ('Cython\\Build\\IpythonMagic.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\IpythonMagic.py',
   'DATA'),
  ('Cython\\Build\\SharedModule.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\SharedModule.py',
   'DATA'),
  ('Cython\\Build\\Tests\\TestCyCache.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Tests\\TestCyCache.py',
   'DATA'),
  ('Cython\\Build\\Tests\\TestCythonizeArgsParser.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Tests\\TestCythonizeArgsParser.py',
   'DATA'),
  ('Cython\\Build\\Tests\\TestDependencies.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Tests\\TestDependencies.py',
   'DATA'),
  ('Cython\\Build\\Tests\\TestInline.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Tests\\TestInline.py',
   'DATA'),
  ('Cython\\Build\\Tests\\TestIpythonMagic.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Tests\\TestIpythonMagic.py',
   'DATA'),
  ('Cython\\Build\\Tests\\TestRecythonize.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Tests\\TestRecythonize.py',
   'DATA'),
  ('Cython\\Build\\Tests\\TestStripLiterals.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Tests\\TestStripLiterals.py',
   'DATA'),
  ('Cython\\Build\\Tests\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\Tests\\__init__.py',
   'DATA'),
  ('Cython\\Build\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Build\\__init__.py',
   'DATA'),
  ('Cython\\CodeWriter.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\CodeWriter.py',
   'DATA'),
  ('Cython\\Compiler\\AnalysedTreeTransforms.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\AnalysedTreeTransforms.py',
   'DATA'),
  ('Cython\\Compiler\\Annotate.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Annotate.py',
   'DATA'),
  ('Cython\\Compiler\\AutoDocTransforms.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\AutoDocTransforms.py',
   'DATA'),
  ('Cython\\Compiler\\Buffer.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Buffer.py',
   'DATA'),
  ('Cython\\Compiler\\Builtin.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Builtin.py',
   'DATA'),
  ('Cython\\Compiler\\CmdLine.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\CmdLine.py',
   'DATA'),
  ('Cython\\Compiler\\Code.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Code.pxd',
   'DATA'),
  ('Cython\\Compiler\\Code.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Code.py',
   'DATA'),
  ('Cython\\Compiler\\CodeGeneration.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\CodeGeneration.py',
   'DATA'),
  ('Cython\\Compiler\\CythonScope.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\CythonScope.py',
   'DATA'),
  ('Cython\\Compiler\\Dataclass.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Dataclass.py',
   'DATA'),
  ('Cython\\Compiler\\DebugFlags.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\DebugFlags.py',
   'DATA'),
  ('Cython\\Compiler\\Errors.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Errors.py',
   'DATA'),
  ('Cython\\Compiler\\ExprNodes.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\ExprNodes.py',
   'DATA'),
  ('Cython\\Compiler\\FlowControl.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\FlowControl.pxd',
   'DATA'),
  ('Cython\\Compiler\\FlowControl.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\FlowControl.py',
   'DATA'),
  ('Cython\\Compiler\\FusedNode.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\FusedNode.py',
   'DATA'),
  ('Cython\\Compiler\\Future.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Future.py',
   'DATA'),
  ('Cython\\Compiler\\Interpreter.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Interpreter.py',
   'DATA'),
  ('Cython\\Compiler\\Lexicon.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Lexicon.py',
   'DATA'),
  ('Cython\\Compiler\\LineTable.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\LineTable.py',
   'DATA'),
  ('Cython\\Compiler\\Main.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Main.py',
   'DATA'),
  ('Cython\\Compiler\\MatchCaseNodes.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\MatchCaseNodes.py',
   'DATA'),
  ('Cython\\Compiler\\MemoryView.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\MemoryView.py',
   'DATA'),
  ('Cython\\Compiler\\ModuleNode.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\ModuleNode.py',
   'DATA'),
  ('Cython\\Compiler\\Naming.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Naming.py',
   'DATA'),
  ('Cython\\Compiler\\Nodes.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Nodes.py',
   'DATA'),
  ('Cython\\Compiler\\Optimize.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Optimize.py',
   'DATA'),
  ('Cython\\Compiler\\Options.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Options.py',
   'DATA'),
  ('Cython\\Compiler\\ParseTreeTransforms.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\ParseTreeTransforms.pxd',
   'DATA'),
  ('Cython\\Compiler\\ParseTreeTransforms.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\ParseTreeTransforms.py',
   'DATA'),
  ('Cython\\Compiler\\Parsing.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Parsing.pxd',
   'DATA'),
  ('Cython\\Compiler\\Parsing.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Parsing.py',
   'DATA'),
  ('Cython\\Compiler\\Pipeline.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Pipeline.py',
   'DATA'),
  ('Cython\\Compiler\\PyrexTypes.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\PyrexTypes.py',
   'DATA'),
  ('Cython\\Compiler\\Pythran.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Pythran.py',
   'DATA'),
  ('Cython\\Compiler\\Scanning.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Scanning.pxd',
   'DATA'),
  ('Cython\\Compiler\\Scanning.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Scanning.py',
   'DATA'),
  ('Cython\\Compiler\\StringEncoding.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\StringEncoding.py',
   'DATA'),
  ('Cython\\Compiler\\Symtab.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Symtab.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestBuffer.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestBuffer.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestBuiltin.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestBuiltin.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestCmdLine.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestCmdLine.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestCode.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestCode.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestFlowControl.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestFlowControl.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestGrammar.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestGrammar.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestMemView.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestMemView.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestParseTreeTransforms.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestParseTreeTransforms.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestScanning.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestScanning.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestSignatureMatching.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestSignatureMatching.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestStringEncoding.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestStringEncoding.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestTreeFragment.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestTreeFragment.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestTreePath.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestTreePath.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestTypes.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestTypes.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestUtilityLoad.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestUtilityLoad.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\TestVisitor.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\TestVisitor.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\Utils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\Utils.py',
   'DATA'),
  ('Cython\\Compiler\\Tests\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Tests\\__init__.py',
   'DATA'),
  ('Cython\\Compiler\\TreeFragment.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\TreeFragment.py',
   'DATA'),
  ('Cython\\Compiler\\TreePath.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\TreePath.py',
   'DATA'),
  ('Cython\\Compiler\\TypeInference.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\TypeInference.py',
   'DATA'),
  ('Cython\\Compiler\\TypeSlots.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\TypeSlots.py',
   'DATA'),
  ('Cython\\Compiler\\UFuncs.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\UFuncs.py',
   'DATA'),
  ('Cython\\Compiler\\UtilNodes.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\UtilNodes.py',
   'DATA'),
  ('Cython\\Compiler\\UtilityCode.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\UtilityCode.py',
   'DATA'),
  ('Cython\\Compiler\\Version.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Version.py',
   'DATA'),
  ('Cython\\Compiler\\Visitor.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Visitor.pxd',
   'DATA'),
  ('Cython\\Compiler\\Visitor.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\Visitor.py',
   'DATA'),
  ('Cython\\Compiler\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Compiler\\__init__.py',
   'DATA'),
  ('Cython\\Coverage.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Coverage.py',
   'DATA'),
  ('Cython\\Debugger\\Cygdb.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\Cygdb.py',
   'DATA'),
  ('Cython\\Debugger\\DebugWriter.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\DebugWriter.py',
   'DATA'),
  ('Cython\\Debugger\\Tests\\TestLibCython.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\Tests\\TestLibCython.py',
   'DATA'),
  ('Cython\\Debugger\\Tests\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\Tests\\__init__.py',
   'DATA'),
  ('Cython\\Debugger\\Tests\\cfuncs.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\Tests\\cfuncs.c',
   'DATA'),
  ('Cython\\Debugger\\Tests\\codefile',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\Tests\\codefile',
   'DATA'),
  ('Cython\\Debugger\\Tests\\test_libcython_in_gdb.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\Tests\\test_libcython_in_gdb.py',
   'DATA'),
  ('Cython\\Debugger\\Tests\\test_libpython_in_gdb.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\Tests\\test_libpython_in_gdb.py',
   'DATA'),
  ('Cython\\Debugger\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\__init__.py',
   'DATA'),
  ('Cython\\Debugger\\libcython.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\libcython.py',
   'DATA'),
  ('Cython\\Debugger\\libpython.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugger\\libpython.py',
   'DATA'),
  ('Cython\\Debugging.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Debugging.py',
   'DATA'),
  ('Cython\\Distutils\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Distutils\\__init__.py',
   'DATA'),
  ('Cython\\Distutils\\build_ext.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Distutils\\build_ext.py',
   'DATA'),
  ('Cython\\Distutils\\extension.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Distutils\\extension.py',
   'DATA'),
  ('Cython\\Distutils\\old_build_ext.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Distutils\\old_build_ext.py',
   'DATA'),
  ('Cython\\Includes\\cpython\\__init__.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\__init__.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\array.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\array.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\bool.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\bool.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\buffer.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\buffer.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\bytearray.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\bytearray.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\bytes.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\bytes.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\cellobject.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\cellobject.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\ceval.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\ceval.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\codecs.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\codecs.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\complex.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\complex.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\contextvars.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\contextvars.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\conversion.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\conversion.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\datetime.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\datetime.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\descr.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\descr.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\dict.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\dict.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\exc.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\exc.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\fileobject.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\fileobject.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\float.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\float.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\function.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\function.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\genobject.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\genobject.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\getargs.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\getargs.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\instance.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\instance.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\iterator.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\iterator.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\iterobject.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\iterobject.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\list.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\list.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\long.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\long.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\longintrepr.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\longintrepr.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\mapping.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\mapping.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\marshal.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\marshal.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\mem.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\mem.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\memoryview.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\memoryview.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\method.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\method.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\module.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\module.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\number.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\number.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\object.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\object.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\pycapsule.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\pycapsule.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\pylifecycle.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\pylifecycle.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\pyport.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\pyport.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\pystate.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\pystate.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\pythread.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\pythread.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\ref.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\ref.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\sequence.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\sequence.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\set.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\set.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\slice.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\slice.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\time.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\time.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\tuple.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\tuple.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\type.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\type.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\unicode.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\unicode.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\version.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\version.pxd',
   'DATA'),
  ('Cython\\Includes\\cpython\\weakref.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\cpython\\weakref.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\__init__.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\__init__.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\complex.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\complex.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\errno.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\errno.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\float.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\float.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\limits.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\limits.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\locale.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\locale.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\math.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\math.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\setjmp.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\setjmp.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\signal.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\signal.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\stddef.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\stddef.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\stdint.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\stdint.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\stdio.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\stdio.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\stdlib.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\stdlib.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\string.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\string.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\threads.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\threads.pxd',
   'DATA'),
  ('Cython\\Includes\\libc\\time.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libc\\time.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\__init__.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\__init__.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\algorithm.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\algorithm.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\any.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\any.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\atomic.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\atomic.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\barrier.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\barrier.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\bit.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\bit.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\cast.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\cast.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\cmath.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\cmath.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\complex.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\complex.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\deque.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\deque.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\exception.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\exception.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\execution.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\execution.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\forward_list.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\forward_list.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\functional.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\functional.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\future.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\future.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\iterator.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\iterator.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\latch.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\latch.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\limits.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\limits.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\list.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\list.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\map.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\map.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\memory.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\memory.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\mutex.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\mutex.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\numbers.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\numbers.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\numeric.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\numeric.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\optional.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\optional.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\pair.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\pair.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\queue.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\queue.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\random.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\random.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\semaphore.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\semaphore.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\set.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\set.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\shared_mutex.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\shared_mutex.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\span.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\span.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\stack.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\stack.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\stop_token.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\stop_token.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\string.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\string.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\string_view.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\string_view.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\typeindex.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\typeindex.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\typeinfo.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\typeinfo.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\unordered_map.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\unordered_map.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\unordered_set.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\unordered_set.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\utility.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\utility.pxd',
   'DATA'),
  ('Cython\\Includes\\libcpp\\vector.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\libcpp\\vector.pxd',
   'DATA'),
  ('Cython\\Includes\\numpy\\math.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\numpy\\math.pxd',
   'DATA'),
  ('Cython\\Includes\\openmp.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\openmp.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\__init__.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\__init__.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\dlfcn.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\dlfcn.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\fcntl.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\fcntl.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\ioctl.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\ioctl.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\mman.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\mman.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\resource.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\resource.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\select.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\select.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\signal.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\signal.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\stat.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\stat.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\stdio.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\stdio.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\stdlib.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\stdlib.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\strings.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\strings.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\time.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\time.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\types.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\types.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\uio.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\uio.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\unistd.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\unistd.pxd',
   'DATA'),
  ('Cython\\Includes\\posix\\wait.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Includes\\posix\\wait.pxd',
   'DATA'),
  ('Cython\\Plex\\Actions.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Actions.pxd',
   'DATA'),
  ('Cython\\Plex\\Actions.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Actions.py',
   'DATA'),
  ('Cython\\Plex\\DFA.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\DFA.pxd',
   'DATA'),
  ('Cython\\Plex\\DFA.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\DFA.py',
   'DATA'),
  ('Cython\\Plex\\Errors.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Errors.py',
   'DATA'),
  ('Cython\\Plex\\Lexicons.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Lexicons.py',
   'DATA'),
  ('Cython\\Plex\\Machines.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Machines.pxd',
   'DATA'),
  ('Cython\\Plex\\Machines.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Machines.py',
   'DATA'),
  ('Cython\\Plex\\Regexps.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Regexps.py',
   'DATA'),
  ('Cython\\Plex\\Scanners.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Scanners.pxd',
   'DATA'),
  ('Cython\\Plex\\Scanners.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Scanners.py',
   'DATA'),
  ('Cython\\Plex\\Transitions.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Transitions.pxd',
   'DATA'),
  ('Cython\\Plex\\Transitions.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\Transitions.py',
   'DATA'),
  ('Cython\\Plex\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Plex\\__init__.py',
   'DATA'),
  ('Cython\\Runtime\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Runtime\\__init__.py',
   'DATA'),
  ('Cython\\Runtime\\refnanny.pyx',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Runtime\\refnanny.pyx',
   'DATA'),
  ('Cython\\Shadow.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Shadow.py',
   'DATA'),
  ('Cython\\Shadow.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Shadow.pyi',
   'DATA'),
  ('Cython\\StringIOTree.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\StringIOTree.py',
   'DATA'),
  ('Cython\\Tempita\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tempita\\__init__.py',
   'DATA'),
  ('Cython\\Tempita\\_looper.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tempita\\_looper.py',
   'DATA'),
  ('Cython\\Tempita\\_tempita.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tempita\\_tempita.py',
   'DATA'),
  ('Cython\\TestUtils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\TestUtils.py',
   'DATA'),
  ('Cython\\Tests\\TestCodeWriter.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tests\\TestCodeWriter.py',
   'DATA'),
  ('Cython\\Tests\\TestCythonUtils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tests\\TestCythonUtils.py',
   'DATA'),
  ('Cython\\Tests\\TestJediTyper.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tests\\TestJediTyper.py',
   'DATA'),
  ('Cython\\Tests\\TestShadow.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tests\\TestShadow.py',
   'DATA'),
  ('Cython\\Tests\\TestStringIOTree.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tests\\TestStringIOTree.py',
   'DATA'),
  ('Cython\\Tests\\TestTestUtils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tests\\TestTestUtils.py',
   'DATA'),
  ('Cython\\Tests\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tests\\__init__.py',
   'DATA'),
  ('Cython\\Tests\\xmlrunner.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Tests\\xmlrunner.py',
   'DATA'),
  ('Cython\\Utility\\AsyncGen.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\AsyncGen.c',
   'DATA'),
  ('Cython\\Utility\\Buffer.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Buffer.c',
   'DATA'),
  ('Cython\\Utility\\BufferFormatFromTypeInfo.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\BufferFormatFromTypeInfo.pxd',
   'DATA'),
  ('Cython\\Utility\\Builtins.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Builtins.c',
   'DATA'),
  ('Cython\\Utility\\CConvert.pyx',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\CConvert.pyx',
   'DATA'),
  ('Cython\\Utility\\CMath.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\CMath.c',
   'DATA'),
  ('Cython\\Utility\\CommonStructures.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\CommonStructures.c',
   'DATA'),
  ('Cython\\Utility\\Complex.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Complex.c',
   'DATA'),
  ('Cython\\Utility\\Coroutine.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Coroutine.c',
   'DATA'),
  ('Cython\\Utility\\CpdefEnums.pyx',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\CpdefEnums.pyx',
   'DATA'),
  ('Cython\\Utility\\CppConvert.pyx',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\CppConvert.pyx',
   'DATA'),
  ('Cython\\Utility\\CppSupport.cpp',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\CppSupport.cpp',
   'DATA'),
  ('Cython\\Utility\\CythonFunction.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\CythonFunction.c',
   'DATA'),
  ('Cython\\Utility\\Dataclasses.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Dataclasses.c',
   'DATA'),
  ('Cython\\Utility\\Dataclasses.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Dataclasses.py',
   'DATA'),
  ('Cython\\Utility\\Embed.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Embed.c',
   'DATA'),
  ('Cython\\Utility\\Exceptions.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Exceptions.c',
   'DATA'),
  ('Cython\\Utility\\ExtensionTypes.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\ExtensionTypes.c',
   'DATA'),
  ('Cython\\Utility\\FunctionArguments.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\FunctionArguments.c',
   'DATA'),
  ('Cython\\Utility\\ImportExport.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\ImportExport.c',
   'DATA'),
  ('Cython\\Utility\\Lock.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Lock.c',
   'DATA'),
  ('Cython\\Utility\\MemoryView.pxd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\MemoryView.pxd',
   'DATA'),
  ('Cython\\Utility\\MemoryView.pyx',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\MemoryView.pyx',
   'DATA'),
  ('Cython\\Utility\\MemoryView_C.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\MemoryView_C.c',
   'DATA'),
  ('Cython\\Utility\\ModuleSetupCode.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\ModuleSetupCode.c',
   'DATA'),
  ('Cython\\Utility\\NumpyImportArray.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\NumpyImportArray.c',
   'DATA'),
  ('Cython\\Utility\\ObjectHandling.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\ObjectHandling.c',
   'DATA'),
  ('Cython\\Utility\\Optimize.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Optimize.c',
   'DATA'),
  ('Cython\\Utility\\Overflow.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Overflow.c',
   'DATA'),
  ('Cython\\Utility\\Printing.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Printing.c',
   'DATA'),
  ('Cython\\Utility\\Profile.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\Profile.c',
   'DATA'),
  ('Cython\\Utility\\StringTools.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\StringTools.c',
   'DATA'),
  ('Cython\\Utility\\TestCyUtilityLoader.pyx',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\TestCyUtilityLoader.pyx',
   'DATA'),
  ('Cython\\Utility\\TestCythonScope.pyx',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\TestCythonScope.pyx',
   'DATA'),
  ('Cython\\Utility\\TestUtilityLoader.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\TestUtilityLoader.c',
   'DATA'),
  ('Cython\\Utility\\TypeConversion.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\TypeConversion.c',
   'DATA'),
  ('Cython\\Utility\\UFuncs.pyx',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\UFuncs.pyx',
   'DATA'),
  ('Cython\\Utility\\UFuncs_C.c',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\UFuncs_C.c',
   'DATA'),
  ('Cython\\Utility\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\__init__.py',
   'DATA'),
  ('Cython\\Utility\\arrayarray.h',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utility\\arrayarray.h',
   'DATA'),
  ('Cython\\Utils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\Utils.py',
   'DATA'),
  ('Cython\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\__init__.py',
   'DATA'),
  ('Cython\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\__init__.pyi',
   'DATA'),
  ('Cython\\py.typed',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\Cython\\py.typed',
   'DATA'),
  ('cython-3.1.2.dist-info\\INSTALLER',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cython-3.1.2.dist-info\\INSTALLER',
   'DATA'),
  ('cython-3.1.2.dist-info\\METADATA',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cython-3.1.2.dist-info\\METADATA',
   'DATA'),
  ('cython-3.1.2.dist-info\\RECORD',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cython-3.1.2.dist-info\\RECORD',
   'DATA'),
  ('cython-3.1.2.dist-info\\WHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cython-3.1.2.dist-info\\WHEEL',
   'DATA'),
  ('cython-3.1.2.dist-info\\entry_points.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cython-3.1.2.dist-info\\entry_points.txt',
   'DATA'),
  ('cython-3.1.2.dist-info\\licenses\\COPYING.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cython-3.1.2.dist-info\\licenses\\COPYING.txt',
   'DATA'),
  ('cython-3.1.2.dist-info\\licenses\\LICENSE.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cython-3.1.2.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('cython-3.1.2.dist-info\\top_level.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cython-3.1.2.dist-info\\top_level.txt',
   'DATA'),
  ('gui\\__init__.py', 'D:\\mac\\视觉监控工具\\dist\\gui\\__init__.py', 'DATA'),
  ('gui\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\gui\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('gui\\__pycache__\\active_dialogue_ui06242.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\gui\\__pycache__\\active_dialogue_ui06242.cpython-310.pyc',
   'DATA'),
  ('gui\\__pycache__\\chat_summary_ui06151.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\gui\\__pycache__\\chat_summary_ui06151.cpython-310.pyc',
   'DATA'),
  ('gui\\__pycache__\\fixed_reply_ui.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\gui\\__pycache__\\fixed_reply_ui.cpython-310.pyc',
   'DATA'),
  ('gui\\__pycache__\\history_ui.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\gui\\__pycache__\\history_ui.cpython-310.pyc',
   'DATA'),
  ('gui\\__pycache__\\prompt_template_manager.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\gui\\__pycache__\\prompt_template_manager.cpython-310.pyc',
   'DATA'),
  ('gui\\__pycache__\\selection_ui.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\gui\\__pycache__\\selection_ui.cpython-310.pyc',
   'DATA'),
  ('gui\\__pycache__\\settings_ui.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\gui\\__pycache__\\settings_ui.cpython-310.pyc',
   'DATA'),
  ('gui\\__pycache__\\ui_main.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\gui\\__pycache__\\ui_main.cpython-310.pyc',
   'DATA'),
  ('gui\\active_dialogue_ui06242.py',
   'D:\\mac\\视觉监控工具\\dist\\gui\\active_dialogue_ui06242.py',
   'DATA'),
  ('gui\\api_settings.py',
   'D:\\mac\\视觉监控工具\\dist\\gui\\api_settings.py',
   'DATA'),
  ('gui\\chat_summary_ui06151.py',
   'D:\\mac\\视觉监控工具\\dist\\gui\\chat_summary_ui06151.py',
   'DATA'),
  ('gui\\fixed_reply_ui.py',
   'D:\\mac\\视觉监控工具\\dist\\gui\\fixed_reply_ui.py',
   'DATA'),
  ('gui\\history_ui.py', 'D:\\mac\\视觉监控工具\\dist\\gui\\history_ui.py', 'DATA'),
  ('gui\\prompt_template_manager.py',
   'D:\\mac\\视觉监控工具\\dist\\gui\\prompt_template_manager.py',
   'DATA'),
  ('gui\\pyarmor_runtime_000000\\__init__.py',
   'D:\\mac\\视觉监控工具\\dist\\gui\\pyarmor_runtime_000000\\__init__.py',
   'DATA'),
  ('gui\\pyarmor_runtime_000000\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\gui\\pyarmor_runtime_000000\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('gui\\selection_ui.py',
   'D:\\mac\\视觉监控工具\\dist\\gui\\selection_ui.py',
   'DATA'),
  ('gui\\settings_ui.py', 'D:\\mac\\视觉监控工具\\dist\\gui\\settings_ui.py', 'DATA'),
  ('gui\\ui_main.py', 'D:\\mac\\视觉监控工具\\dist\\gui\\ui_main.py', 'DATA'),
  ('langchain_core\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\__init__.py',
   'DATA'),
  ('langchain_core\\_api\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\_api\\__init__.py',
   'DATA'),
  ('langchain_core\\_api\\beta_decorator.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\_api\\beta_decorator.py',
   'DATA'),
  ('langchain_core\\_api\\deprecation.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\_api\\deprecation.py',
   'DATA'),
  ('langchain_core\\_api\\internal.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\_api\\internal.py',
   'DATA'),
  ('langchain_core\\_api\\path.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\_api\\path.py',
   'DATA'),
  ('langchain_core\\_import_utils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\_import_utils.py',
   'DATA'),
  ('langchain_core\\agents.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\agents.py',
   'DATA'),
  ('langchain_core\\beta\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\beta\\__init__.py',
   'DATA'),
  ('langchain_core\\beta\\runnables\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\beta\\runnables\\__init__.py',
   'DATA'),
  ('langchain_core\\beta\\runnables\\context.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\beta\\runnables\\context.py',
   'DATA'),
  ('langchain_core\\caches.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\caches.py',
   'DATA'),
  ('langchain_core\\callbacks\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\callbacks\\__init__.py',
   'DATA'),
  ('langchain_core\\callbacks\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\callbacks\\base.py',
   'DATA'),
  ('langchain_core\\callbacks\\file.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\callbacks\\file.py',
   'DATA'),
  ('langchain_core\\callbacks\\manager.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\callbacks\\manager.py',
   'DATA'),
  ('langchain_core\\callbacks\\stdout.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\callbacks\\stdout.py',
   'DATA'),
  ('langchain_core\\callbacks\\streaming_stdout.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\callbacks\\streaming_stdout.py',
   'DATA'),
  ('langchain_core\\callbacks\\usage.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\callbacks\\usage.py',
   'DATA'),
  ('langchain_core\\chat_history.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\chat_history.py',
   'DATA'),
  ('langchain_core\\chat_loaders.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\chat_loaders.py',
   'DATA'),
  ('langchain_core\\chat_sessions.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\chat_sessions.py',
   'DATA'),
  ('langchain_core\\document_loaders\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\document_loaders\\__init__.py',
   'DATA'),
  ('langchain_core\\document_loaders\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\document_loaders\\base.py',
   'DATA'),
  ('langchain_core\\document_loaders\\blob_loaders.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\document_loaders\\blob_loaders.py',
   'DATA'),
  ('langchain_core\\document_loaders\\langsmith.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\document_loaders\\langsmith.py',
   'DATA'),
  ('langchain_core\\documents\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\documents\\__init__.py',
   'DATA'),
  ('langchain_core\\documents\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\documents\\base.py',
   'DATA'),
  ('langchain_core\\documents\\compressor.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\documents\\compressor.py',
   'DATA'),
  ('langchain_core\\documents\\transformers.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\documents\\transformers.py',
   'DATA'),
  ('langchain_core\\embeddings\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\embeddings\\__init__.py',
   'DATA'),
  ('langchain_core\\embeddings\\embeddings.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\embeddings\\embeddings.py',
   'DATA'),
  ('langchain_core\\embeddings\\fake.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\embeddings\\fake.py',
   'DATA'),
  ('langchain_core\\env.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\env.py',
   'DATA'),
  ('langchain_core\\example_selectors\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\example_selectors\\__init__.py',
   'DATA'),
  ('langchain_core\\example_selectors\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\example_selectors\\base.py',
   'DATA'),
  ('langchain_core\\example_selectors\\length_based.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\example_selectors\\length_based.py',
   'DATA'),
  ('langchain_core\\example_selectors\\semantic_similarity.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\example_selectors\\semantic_similarity.py',
   'DATA'),
  ('langchain_core\\exceptions.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\exceptions.py',
   'DATA'),
  ('langchain_core\\globals.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\globals.py',
   'DATA'),
  ('langchain_core\\indexing\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\indexing\\__init__.py',
   'DATA'),
  ('langchain_core\\indexing\\api.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\indexing\\api.py',
   'DATA'),
  ('langchain_core\\indexing\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\indexing\\base.py',
   'DATA'),
  ('langchain_core\\indexing\\in_memory.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\indexing\\in_memory.py',
   'DATA'),
  ('langchain_core\\language_models\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\language_models\\__init__.py',
   'DATA'),
  ('langchain_core\\language_models\\_utils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\language_models\\_utils.py',
   'DATA'),
  ('langchain_core\\language_models\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\language_models\\base.py',
   'DATA'),
  ('langchain_core\\language_models\\chat_models.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\language_models\\chat_models.py',
   'DATA'),
  ('langchain_core\\language_models\\fake.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\language_models\\fake.py',
   'DATA'),
  ('langchain_core\\language_models\\fake_chat_models.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\language_models\\fake_chat_models.py',
   'DATA'),
  ('langchain_core\\language_models\\llms.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\language_models\\llms.py',
   'DATA'),
  ('langchain_core\\load\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\load\\__init__.py',
   'DATA'),
  ('langchain_core\\load\\dump.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\load\\dump.py',
   'DATA'),
  ('langchain_core\\load\\load.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\load\\load.py',
   'DATA'),
  ('langchain_core\\load\\mapping.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\load\\mapping.py',
   'DATA'),
  ('langchain_core\\load\\serializable.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\load\\serializable.py',
   'DATA'),
  ('langchain_core\\memory.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\memory.py',
   'DATA'),
  ('langchain_core\\messages\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\__init__.py',
   'DATA'),
  ('langchain_core\\messages\\ai.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\ai.py',
   'DATA'),
  ('langchain_core\\messages\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\base.py',
   'DATA'),
  ('langchain_core\\messages\\chat.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\chat.py',
   'DATA'),
  ('langchain_core\\messages\\content_blocks.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\content_blocks.py',
   'DATA'),
  ('langchain_core\\messages\\function.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\function.py',
   'DATA'),
  ('langchain_core\\messages\\human.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\human.py',
   'DATA'),
  ('langchain_core\\messages\\modifier.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\modifier.py',
   'DATA'),
  ('langchain_core\\messages\\system.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\system.py',
   'DATA'),
  ('langchain_core\\messages\\tool.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\tool.py',
   'DATA'),
  ('langchain_core\\messages\\utils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\messages\\utils.py',
   'DATA'),
  ('langchain_core\\output_parsers\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\__init__.py',
   'DATA'),
  ('langchain_core\\output_parsers\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\base.py',
   'DATA'),
  ('langchain_core\\output_parsers\\format_instructions.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\format_instructions.py',
   'DATA'),
  ('langchain_core\\output_parsers\\json.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\json.py',
   'DATA'),
  ('langchain_core\\output_parsers\\list.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\list.py',
   'DATA'),
  ('langchain_core\\output_parsers\\openai_functions.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\openai_functions.py',
   'DATA'),
  ('langchain_core\\output_parsers\\openai_tools.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\openai_tools.py',
   'DATA'),
  ('langchain_core\\output_parsers\\pydantic.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\pydantic.py',
   'DATA'),
  ('langchain_core\\output_parsers\\string.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\string.py',
   'DATA'),
  ('langchain_core\\output_parsers\\transform.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\transform.py',
   'DATA'),
  ('langchain_core\\output_parsers\\xml.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\output_parsers\\xml.py',
   'DATA'),
  ('langchain_core\\outputs\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\outputs\\__init__.py',
   'DATA'),
  ('langchain_core\\outputs\\chat_generation.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\outputs\\chat_generation.py',
   'DATA'),
  ('langchain_core\\outputs\\chat_result.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\outputs\\chat_result.py',
   'DATA'),
  ('langchain_core\\outputs\\generation.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\outputs\\generation.py',
   'DATA'),
  ('langchain_core\\outputs\\llm_result.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\outputs\\llm_result.py',
   'DATA'),
  ('langchain_core\\outputs\\run_info.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\outputs\\run_info.py',
   'DATA'),
  ('langchain_core\\prompt_values.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompt_values.py',
   'DATA'),
  ('langchain_core\\prompts\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\__init__.py',
   'DATA'),
  ('langchain_core\\prompts\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\base.py',
   'DATA'),
  ('langchain_core\\prompts\\chat.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\chat.py',
   'DATA'),
  ('langchain_core\\prompts\\dict.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\dict.py',
   'DATA'),
  ('langchain_core\\prompts\\few_shot.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\few_shot.py',
   'DATA'),
  ('langchain_core\\prompts\\few_shot_with_templates.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\few_shot_with_templates.py',
   'DATA'),
  ('langchain_core\\prompts\\image.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\image.py',
   'DATA'),
  ('langchain_core\\prompts\\loading.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\loading.py',
   'DATA'),
  ('langchain_core\\prompts\\message.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\message.py',
   'DATA'),
  ('langchain_core\\prompts\\pipeline.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\pipeline.py',
   'DATA'),
  ('langchain_core\\prompts\\prompt.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\prompt.py',
   'DATA'),
  ('langchain_core\\prompts\\string.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\string.py',
   'DATA'),
  ('langchain_core\\prompts\\structured.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\prompts\\structured.py',
   'DATA'),
  ('langchain_core\\py.typed',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\py.typed',
   'DATA'),
  ('langchain_core\\pydantic_v1\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\pydantic_v1\\__init__.py',
   'DATA'),
  ('langchain_core\\pydantic_v1\\dataclasses.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\pydantic_v1\\dataclasses.py',
   'DATA'),
  ('langchain_core\\pydantic_v1\\main.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\pydantic_v1\\main.py',
   'DATA'),
  ('langchain_core\\rate_limiters.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\rate_limiters.py',
   'DATA'),
  ('langchain_core\\retrievers.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\retrievers.py',
   'DATA'),
  ('langchain_core\\runnables\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\__init__.py',
   'DATA'),
  ('langchain_core\\runnables\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\base.py',
   'DATA'),
  ('langchain_core\\runnables\\branch.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\branch.py',
   'DATA'),
  ('langchain_core\\runnables\\config.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\config.py',
   'DATA'),
  ('langchain_core\\runnables\\configurable.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\configurable.py',
   'DATA'),
  ('langchain_core\\runnables\\fallbacks.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\fallbacks.py',
   'DATA'),
  ('langchain_core\\runnables\\graph.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\graph.py',
   'DATA'),
  ('langchain_core\\runnables\\graph_ascii.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\graph_ascii.py',
   'DATA'),
  ('langchain_core\\runnables\\graph_mermaid.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\graph_mermaid.py',
   'DATA'),
  ('langchain_core\\runnables\\graph_png.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\graph_png.py',
   'DATA'),
  ('langchain_core\\runnables\\history.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\history.py',
   'DATA'),
  ('langchain_core\\runnables\\passthrough.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\passthrough.py',
   'DATA'),
  ('langchain_core\\runnables\\retry.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\retry.py',
   'DATA'),
  ('langchain_core\\runnables\\router.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\router.py',
   'DATA'),
  ('langchain_core\\runnables\\schema.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\schema.py',
   'DATA'),
  ('langchain_core\\runnables\\utils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\runnables\\utils.py',
   'DATA'),
  ('langchain_core\\stores.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\stores.py',
   'DATA'),
  ('langchain_core\\structured_query.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\structured_query.py',
   'DATA'),
  ('langchain_core\\sys_info.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\sys_info.py',
   'DATA'),
  ('langchain_core\\tools\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tools\\__init__.py',
   'DATA'),
  ('langchain_core\\tools\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tools\\base.py',
   'DATA'),
  ('langchain_core\\tools\\convert.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tools\\convert.py',
   'DATA'),
  ('langchain_core\\tools\\render.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tools\\render.py',
   'DATA'),
  ('langchain_core\\tools\\retriever.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tools\\retriever.py',
   'DATA'),
  ('langchain_core\\tools\\simple.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tools\\simple.py',
   'DATA'),
  ('langchain_core\\tools\\structured.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tools\\structured.py',
   'DATA'),
  ('langchain_core\\tracers\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\__init__.py',
   'DATA'),
  ('langchain_core\\tracers\\_streaming.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\_streaming.py',
   'DATA'),
  ('langchain_core\\tracers\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\base.py',
   'DATA'),
  ('langchain_core\\tracers\\context.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\context.py',
   'DATA'),
  ('langchain_core\\tracers\\core.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\core.py',
   'DATA'),
  ('langchain_core\\tracers\\evaluation.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\evaluation.py',
   'DATA'),
  ('langchain_core\\tracers\\event_stream.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\event_stream.py',
   'DATA'),
  ('langchain_core\\tracers\\langchain.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\langchain.py',
   'DATA'),
  ('langchain_core\\tracers\\langchain_v1.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\langchain_v1.py',
   'DATA'),
  ('langchain_core\\tracers\\log_stream.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\log_stream.py',
   'DATA'),
  ('langchain_core\\tracers\\memory_stream.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\memory_stream.py',
   'DATA'),
  ('langchain_core\\tracers\\root_listeners.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\root_listeners.py',
   'DATA'),
  ('langchain_core\\tracers\\run_collector.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\run_collector.py',
   'DATA'),
  ('langchain_core\\tracers\\schemas.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\schemas.py',
   'DATA'),
  ('langchain_core\\tracers\\stdout.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\tracers\\stdout.py',
   'DATA'),
  ('langchain_core\\utils\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\__init__.py',
   'DATA'),
  ('langchain_core\\utils\\_merge.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\_merge.py',
   'DATA'),
  ('langchain_core\\utils\\aiter.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\aiter.py',
   'DATA'),
  ('langchain_core\\utils\\env.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\env.py',
   'DATA'),
  ('langchain_core\\utils\\formatting.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\formatting.py',
   'DATA'),
  ('langchain_core\\utils\\function_calling.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\function_calling.py',
   'DATA'),
  ('langchain_core\\utils\\html.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\html.py',
   'DATA'),
  ('langchain_core\\utils\\image.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\image.py',
   'DATA'),
  ('langchain_core\\utils\\input.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\input.py',
   'DATA'),
  ('langchain_core\\utils\\interactive_env.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\interactive_env.py',
   'DATA'),
  ('langchain_core\\utils\\iter.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\iter.py',
   'DATA'),
  ('langchain_core\\utils\\json.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\json.py',
   'DATA'),
  ('langchain_core\\utils\\json_schema.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\json_schema.py',
   'DATA'),
  ('langchain_core\\utils\\loading.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\loading.py',
   'DATA'),
  ('langchain_core\\utils\\mustache.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\mustache.py',
   'DATA'),
  ('langchain_core\\utils\\pydantic.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\pydantic.py',
   'DATA'),
  ('langchain_core\\utils\\strings.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\strings.py',
   'DATA'),
  ('langchain_core\\utils\\usage.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\usage.py',
   'DATA'),
  ('langchain_core\\utils\\utils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\utils\\utils.py',
   'DATA'),
  ('langchain_core\\vectorstores\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\vectorstores\\__init__.py',
   'DATA'),
  ('langchain_core\\vectorstores\\base.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\vectorstores\\base.py',
   'DATA'),
  ('langchain_core\\vectorstores\\in_memory.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\vectorstores\\in_memory.py',
   'DATA'),
  ('langchain_core\\vectorstores\\utils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\vectorstores\\utils.py',
   'DATA'),
  ('langchain_core\\version.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\langchain_core\\version.py',
   'DATA'),
  ('monitor\\__init__.py',
   'D:\\mac\\视觉监控工具\\dist\\monitor\\__init__.py',
   'DATA'),
  ('monitor\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\monitor\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('monitor\\__pycache__\\area_monitor.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\monitor\\__pycache__\\area_monitor.cpython-310.pyc',
   'DATA'),
  ('monitor\\area_monitor.py',
   'D:\\mac\\视觉监控工具\\dist\\monitor\\area_monitor.py',
   'DATA'),
  ('monitor\\pyarmor_runtime_000000\\__init__.py',
   'D:\\mac\\视觉监控工具\\dist\\monitor\\pyarmor_runtime_000000\\__init__.py',
   'DATA'),
  ('monitor\\pyarmor_runtime_000000\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\monitor\\pyarmor_runtime_000000\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('ocr\\__init__.py', 'D:\\mac\\视觉监控工具\\dist\\ocr\\__init__.py', 'DATA'),
  ('ocr\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\ocr\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('ocr\\__pycache__\\message_extractor.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\ocr\\__pycache__\\message_extractor.cpython-310.pyc',
   'DATA'),
  ('ocr\\message_extractor.py',
   'D:\\mac\\视觉监控工具\\dist\\ocr\\message_extractor.py',
   'DATA'),
  ('ocr\\pyarmor_runtime_000000\\__init__.py',
   'D:\\mac\\视觉监控工具\\dist\\ocr\\pyarmor_runtime_000000\\__init__.py',
   'DATA'),
  ('ocr\\pyarmor_runtime_000000\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\ocr\\pyarmor_runtime_000000\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('paddleocr-2.10.0.dist-info\\INSTALLER',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr-2.10.0.dist-info\\INSTALLER',
   'DATA'),
  ('paddleocr-2.10.0.dist-info\\LICENSE',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr-2.10.0.dist-info\\LICENSE',
   'DATA'),
  ('paddleocr-2.10.0.dist-info\\METADATA',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr-2.10.0.dist-info\\METADATA',
   'DATA'),
  ('paddleocr-2.10.0.dist-info\\RECORD',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr-2.10.0.dist-info\\RECORD',
   'DATA'),
  ('paddleocr-2.10.0.dist-info\\REQUESTED',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr-2.10.0.dist-info\\REQUESTED',
   'DATA'),
  ('paddleocr-2.10.0.dist-info\\WHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr-2.10.0.dist-info\\WHEEL',
   'DATA'),
  ('paddleocr-2.10.0.dist-info\\entry_points.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr-2.10.0.dist-info\\entry_points.txt',
   'DATA'),
  ('paddleocr-2.10.0.dist-info\\top_level.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr-2.10.0.dist-info\\top_level.txt',
   'DATA'),
  ('paddleocr\\LICENSE',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\LICENSE',
   'DATA'),
  ('paddleocr\\MANIFEST.in',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\MANIFEST.in',
   'DATA'),
  ('paddleocr\\README.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\README.md',
   'DATA'),
  ('paddleocr\\README_en.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\README_en.md',
   'DATA'),
  ('paddleocr\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\__init__.py',
   'DATA'),
  ('paddleocr\\paddleocr.egg-info\\PKG-INFO',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\paddleocr.egg-info\\PKG-INFO',
   'DATA'),
  ('paddleocr\\paddleocr.egg-info\\SOURCES.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\paddleocr.egg-info\\SOURCES.txt',
   'DATA'),
  ('paddleocr\\paddleocr.egg-info\\dependency_links.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\paddleocr.egg-info\\dependency_links.txt',
   'DATA'),
  ('paddleocr\\paddleocr.egg-info\\entry_points.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\paddleocr.egg-info\\entry_points.txt',
   'DATA'),
  ('paddleocr\\paddleocr.egg-info\\requires.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\paddleocr.egg-info\\requires.txt',
   'DATA'),
  ('paddleocr\\paddleocr.egg-info\\top_level.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\paddleocr.egg-info\\top_level.txt',
   'DATA'),
  ('paddleocr\\paddleocr.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\paddleocr.py',
   'DATA'),
  ('paddleocr\\ppocr\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\collate_fn.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\collate_fn.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\ColorJitter.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\ColorJitter.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\abinet_aug.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\abinet_aug.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\copy_paste.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\copy_paste.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\ct_process.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\ct_process.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\drrg_targets.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\drrg_targets.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\east_process.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\east_process.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\fce_aug.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\fce_aug.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\fce_targets.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\fce_targets.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\frost_img\\frost1.jpg',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\frost_img\\frost1.jpg',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\frost_img\\frost2.png',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\frost_img\\frost2.png',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\frost_img\\frost3.png',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\frost_img\\frost3.png',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\frost_img\\frost4.jpg',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\frost_img\\frost4.jpg',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\frost_img\\frost5.jpg',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\frost_img\\frost5.jpg',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\frost_img\\frost6.jpg',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\frost_img\\frost6.jpg',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\iaa_augment.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\iaa_augment.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\label_ops.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\label_ops.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\latex_ocr_aug.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\latex_ocr_aug.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\make_border_map.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\make_border_map.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\make_pse_gt.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\make_pse_gt.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\make_shrink_map.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\make_shrink_map.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\operators.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\operators.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\pg_process.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\pg_process.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\randaugment.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\randaugment.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\random_crop_data.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\random_crop_data.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\rec_img_aug.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\rec_img_aug.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\sast_process.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\sast_process.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\ssl_img_aug.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\ssl_img_aug.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\table_ops.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\table_ops.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\text_image_aug\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\text_image_aug\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\text_image_aug\\augment.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\text_image_aug\\augment.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\text_image_aug\\warp_mls.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\text_image_aug\\warp_mls.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\unimernet_aug.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\unimernet_aug.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\vqa\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\vqa\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\vqa\\augment.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\vqa\\augment.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\vqa\\token\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\vqa\\token\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\vqa\\token\\vqa_re_convert.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\vqa\\token\\vqa_re_convert.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\vqa\\token\\vqa_token_chunk.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\vqa\\token\\vqa_token_chunk.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\vqa\\token\\vqa_token_pad.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\vqa\\token\\vqa_token_pad.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\imaug\\vqa\\token\\vqa_token_relation.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\imaug\\vqa\\token\\vqa_token_relation.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\latexocr_dataset.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\latexocr_dataset.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\lmdb_dataset.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\lmdb_dataset.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\multi_scale_sampler.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\multi_scale_sampler.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\pgnet_dataset.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\pgnet_dataset.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\pubtab_dataset.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\pubtab_dataset.py',
   'DATA'),
  ('paddleocr\\ppocr\\data\\simple_dataset.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\data\\simple_dataset.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\cls_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\cls_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\ct_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\ct_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\db_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\db_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\drrg_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\drrg_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\east_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\east_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\fce_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\fce_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\locality_aware_nms.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\locality_aware_nms.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\pg_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\pg_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\picodet_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\picodet_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\pse_postprocess\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\pse_postprocess\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\pse_postprocess\\pse\\README.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\pse_postprocess\\pse\\README.md',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\pse_postprocess\\pse\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\pse_postprocess\\pse\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\pse_postprocess\\pse\\pse.pyx',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\pse_postprocess\\pse\\pse.pyx',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\pse_postprocess\\pse\\setup.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\pse_postprocess\\pse\\setup.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\pse_postprocess\\pse_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\pse_postprocess\\pse_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\rec_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\rec_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\sast_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\sast_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\table_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\table_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\vqa_token_re_layoutlm_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\vqa_token_re_layoutlm_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\postprocess\\vqa_token_ser_layoutlm_postprocess.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\postprocess\\vqa_token_ser_layoutlm_postprocess.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\EN_symbol_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\EN_symbol_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict90.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict90.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\README.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\README.md',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\ar_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\ar_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\arabic_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\arabic_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\be_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\be_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\bengali_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\bengali_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\bg_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\bg_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\bm_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\bm_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\bm_dict_add.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\bm_dict_add.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\bn_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\bn_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\chinese_cht_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\chinese_cht_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\confuse.pkl',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\confuse.pkl',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\cyrillic_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\cyrillic_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\devanagari_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\devanagari_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\en_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\en_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\fa_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\fa_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\french_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\french_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\german_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\german_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\gujarati_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\gujarati_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\hebrew_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\hebrew_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\hi_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\hi_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\it_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\it_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\japan_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\japan_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\ka_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\ka_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\kazakh_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\kazakh_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\kie_dict\\xfund_class_list.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\kie_dict\\xfund_class_list.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\korean_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\korean_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\latex_ocr_tokenizer.json',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\latex_ocr_tokenizer.json',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\latex_symbol_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\latex_symbol_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\latin_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\latin_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\layout_dict\\layout_cdla_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\layout_dict\\layout_cdla_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\layout_dict\\layout_publaynet_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\layout_dict\\layout_publaynet_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\layout_dict\\layout_table_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\layout_dict\\layout_table_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\mr_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\mr_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\ne_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\ne_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\oc_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\oc_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\parseq_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\parseq_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\ppocrv4_doc_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\ppocrv4_doc_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\pu_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\pu_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\rs_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\rs_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\rsc_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\rsc_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\ru_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\ru_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\samaritan_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\samaritan_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\spin_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\spin_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\syriac_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\syriac_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\ta_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\ta_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\table_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\table_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\table_master_structure_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\table_master_structure_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\table_structure_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\table_structure_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\table_structure_dict_ch.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\table_structure_dict_ch.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\te_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\te_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\th_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\th_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\ug_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\ug_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\uk_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\uk_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\unimernet_tokenizer\\tokenizer.json',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\unimernet_tokenizer\\tokenizer.json',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\unimernet_tokenizer\\tokenizer_config.json',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\unimernet_tokenizer\\tokenizer_config.json',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\ur_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\ur_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\vi_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\vi_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\dict\\xi_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\dict\\xi_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\e2e_metric\\Deteval.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\e2e_metric\\Deteval.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\e2e_metric\\polygon_fast.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\e2e_metric\\polygon_fast.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\e2e_utils\\extract_batchsize.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\e2e_utils\\extract_batchsize.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\e2e_utils\\extract_textpoint_fast.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\e2e_utils\\extract_textpoint_fast.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\e2e_utils\\extract_textpoint_slow.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\e2e_utils\\extract_textpoint_slow.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\e2e_utils\\pgnet_pp_utils.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\e2e_utils\\pgnet_pp_utils.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\e2e_utils\\visual.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\e2e_utils\\visual.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\en_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\en_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\export_model.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\export_model.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\formula_utils\\math_txt2pkl.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\formula_utils\\math_txt2pkl.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\formula_utils\\unimernet_data_convert.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\formula_utils\\unimernet_data_convert.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\gen_label.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\gen_label.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\ic15_dict.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\ic15_dict.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\iou.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\iou.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\loggers\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\loggers\\__init__.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\loggers\\base_logger.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\loggers\\base_logger.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\loggers\\loggers.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\loggers\\loggers.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\loggers\\wandb_logger.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\loggers\\wandb_logger.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\logging.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\logging.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\network.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\network.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\poly_nms.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\poly_nms.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\ppocr_keys_v1.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\ppocr_keys_v1.txt',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\profiler.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\profiler.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\save_load.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\save_load.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\stats.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\stats.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\utility.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\utility.py',
   'DATA'),
  ('paddleocr\\ppocr\\utils\\visual.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppocr\\utils\\visual.py',
   'DATA'),
  ('paddleocr\\ppstructure\\README.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\README.md',
   'DATA'),
  ('paddleocr\\ppstructure\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\__init__.py',
   'DATA'),
  ('paddleocr\\ppstructure\\kie\\README.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\kie\\README.md',
   'DATA'),
  ('paddleocr\\ppstructure\\kie\\README_ch.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\kie\\README_ch.md',
   'DATA'),
  ('paddleocr\\ppstructure\\kie\\how_to_do_kie.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\kie\\how_to_do_kie.md',
   'DATA'),
  ('paddleocr\\ppstructure\\kie\\how_to_do_kie_en.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\kie\\how_to_do_kie_en.md',
   'DATA'),
  ('paddleocr\\ppstructure\\kie\\predict_kie_token_ser.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\kie\\predict_kie_token_ser.py',
   'DATA'),
  ('paddleocr\\ppstructure\\kie\\predict_kie_token_ser_re.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\kie\\predict_kie_token_ser_re.py',
   'DATA'),
  ('paddleocr\\ppstructure\\kie\\requirements.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\kie\\requirements.txt',
   'DATA'),
  ('paddleocr\\ppstructure\\kie\\tools\\eval_with_label_end2end.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\kie\\tools\\eval_with_label_end2end.py',
   'DATA'),
  ('paddleocr\\ppstructure\\kie\\tools\\trans_funsd_label.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\kie\\tools\\trans_funsd_label.py',
   'DATA'),
  ('paddleocr\\ppstructure\\kie\\tools\\trans_xfun_data.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\kie\\tools\\trans_xfun_data.py',
   'DATA'),
  ('paddleocr\\ppstructure\\layout\\README.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\layout\\README.md',
   'DATA'),
  ('paddleocr\\ppstructure\\layout\\README_ch.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\layout\\README_ch.md',
   'DATA'),
  ('paddleocr\\ppstructure\\layout\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\layout\\__init__.py',
   'DATA'),
  ('paddleocr\\ppstructure\\layout\\predict_layout.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\layout\\predict_layout.py',
   'DATA'),
  ('paddleocr\\ppstructure\\pdf2word\\README.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\pdf2word\\README.md',
   'DATA'),
  ('paddleocr\\ppstructure\\pdf2word\\icons\\chinese.png',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\pdf2word\\icons\\chinese.png',
   'DATA'),
  ('paddleocr\\ppstructure\\pdf2word\\icons\\english.png',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\pdf2word\\icons\\english.png',
   'DATA'),
  ('paddleocr\\ppstructure\\pdf2word\\icons\\folder-open.png',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\pdf2word\\icons\\folder-open.png',
   'DATA'),
  ('paddleocr\\ppstructure\\pdf2word\\icons\\folder-plus.png',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\pdf2word\\icons\\folder-plus.png',
   'DATA'),
  ('paddleocr\\ppstructure\\pdf2word\\pdf2word.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\pdf2word\\pdf2word.py',
   'DATA'),
  ('paddleocr\\ppstructure\\predict_system.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\predict_system.py',
   'DATA'),
  ('paddleocr\\ppstructure\\recovery\\README.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\recovery\\README.md',
   'DATA'),
  ('paddleocr\\ppstructure\\recovery\\README_ch.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\recovery\\README_ch.md',
   'DATA'),
  ('paddleocr\\ppstructure\\recovery\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\recovery\\__init__.py',
   'DATA'),
  ('paddleocr\\ppstructure\\recovery\\recovery_to_doc.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\recovery\\recovery_to_doc.py',
   'DATA'),
  ('paddleocr\\ppstructure\\recovery\\recovery_to_markdown.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\recovery\\recovery_to_markdown.py',
   'DATA'),
  ('paddleocr\\ppstructure\\recovery\\requirements.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\recovery\\requirements.txt',
   'DATA'),
  ('paddleocr\\ppstructure\\recovery\\table_process.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\recovery\\table_process.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\README.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\README.md',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\README_ch.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\README_ch.md',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\__init__.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\convert_label2html.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\convert_label2html.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\eval_table.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\eval_table.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\matcher.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\matcher.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\predict_structure.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\predict_structure.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\predict_table.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\predict_table.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\table_master_match.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\table_master_match.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\table_metric\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\table_metric\\__init__.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\table_metric\\parallel.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\table_metric\\parallel.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\table_metric\\table_metric.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\table_metric\\table_metric.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\tablepyxl\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\tablepyxl\\__init__.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\tablepyxl\\style.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\tablepyxl\\style.py',
   'DATA'),
  ('paddleocr\\ppstructure\\table\\tablepyxl\\tablepyxl.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\table\\tablepyxl\\tablepyxl.py',
   'DATA'),
  ('paddleocr\\ppstructure\\utility.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\ppstructure\\utility.py',
   'DATA'),
  ('paddleocr\\pyproject.toml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\pyproject.toml',
   'DATA'),
  ('paddleocr\\requirements.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\requirements.txt',
   'DATA'),
  ('paddleocr\\setup.cfg',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\setup.cfg',
   'DATA'),
  ('paddleocr\\setup.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\setup.py',
   'DATA'),
  ('paddleocr\\tools\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\__init__.py',
   'DATA'),
  ('paddleocr\\tools\\end2end\\convert_ppocr_label.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\end2end\\convert_ppocr_label.py',
   'DATA'),
  ('paddleocr\\tools\\end2end\\draw_html.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\end2end\\draw_html.py',
   'DATA'),
  ('paddleocr\\tools\\end2end\\eval_end2end.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\end2end\\eval_end2end.py',
   'DATA'),
  ('paddleocr\\tools\\end2end\\readme.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\end2end\\readme.md',
   'DATA'),
  ('paddleocr\\tools\\eval.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\eval.py',
   'DATA'),
  ('paddleocr\\tools\\export_center.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\export_center.py',
   'DATA'),
  ('paddleocr\\tools\\export_model.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\export_model.py',
   'DATA'),
  ('paddleocr\\tools\\infer\\predict_cls.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer\\predict_cls.py',
   'DATA'),
  ('paddleocr\\tools\\infer\\predict_det.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer\\predict_det.py',
   'DATA'),
  ('paddleocr\\tools\\infer\\predict_e2e.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer\\predict_e2e.py',
   'DATA'),
  ('paddleocr\\tools\\infer\\predict_rec.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer\\predict_rec.py',
   'DATA'),
  ('paddleocr\\tools\\infer\\predict_sr.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer\\predict_sr.py',
   'DATA'),
  ('paddleocr\\tools\\infer\\predict_system.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer\\predict_system.py',
   'DATA'),
  ('paddleocr\\tools\\infer\\utility.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer\\utility.py',
   'DATA'),
  ('paddleocr\\tools\\infer_cls.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer_cls.py',
   'DATA'),
  ('paddleocr\\tools\\infer_det.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer_det.py',
   'DATA'),
  ('paddleocr\\tools\\infer_e2e.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer_e2e.py',
   'DATA'),
  ('paddleocr\\tools\\infer_kie.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer_kie.py',
   'DATA'),
  ('paddleocr\\tools\\infer_kie_token_ser.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer_kie_token_ser.py',
   'DATA'),
  ('paddleocr\\tools\\infer_kie_token_ser_re.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer_kie_token_ser_re.py',
   'DATA'),
  ('paddleocr\\tools\\infer_rec.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer_rec.py',
   'DATA'),
  ('paddleocr\\tools\\infer_sr.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer_sr.py',
   'DATA'),
  ('paddleocr\\tools\\infer_table.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\infer_table.py',
   'DATA'),
  ('paddleocr\\tools\\naive_sync_bn.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\naive_sync_bn.py',
   'DATA'),
  ('paddleocr\\tools\\program.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\program.py',
   'DATA'),
  ('paddleocr\\tools\\test_hubserving.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\test_hubserving.py',
   'DATA'),
  ('paddleocr\\tools\\train.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\paddleocr\\tools\\train.py',
   'DATA'),
  ('tkinter\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\__init__.py',
   'DATA'),
  ('tkinter\\__main__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\__main__.py',
   'DATA'),
  ('tkinter\\colorchooser.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\colorchooser.py',
   'DATA'),
  ('tkinter\\commondialog.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\commondialog.py',
   'DATA'),
  ('tkinter\\constants.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\constants.py',
   'DATA'),
  ('tkinter\\dialog.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\dialog.py',
   'DATA'),
  ('tkinter\\dnd.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\dnd.py',
   'DATA'),
  ('tkinter\\filedialog.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\filedialog.py',
   'DATA'),
  ('tkinter\\font.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\font.py',
   'DATA'),
  ('tkinter\\messagebox.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\messagebox.py',
   'DATA'),
  ('tkinter\\scrolledtext.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\scrolledtext.py',
   'DATA'),
  ('tkinter\\simpledialog.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\simpledialog.py',
   'DATA'),
  ('tkinter\\test\\README',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\README',
   'DATA'),
  ('tkinter\\test\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\__init__.py',
   'DATA'),
  ('tkinter\\test\\support.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\support.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\__init__.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_colorchooser.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_colorchooser.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_font.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_font.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_geometry_managers.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_geometry_managers.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_images.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_images.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_loadtk.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_loadtk.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_messagebox.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_messagebox.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_misc.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_misc.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_simpledialog.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_simpledialog.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_text.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_text.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_variables.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_variables.py',
   'DATA'),
  ('tkinter\\test\\test_tkinter\\test_widgets.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_tkinter\\test_widgets.py',
   'DATA'),
  ('tkinter\\test\\test_ttk\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_ttk\\__init__.py',
   'DATA'),
  ('tkinter\\test\\test_ttk\\test_extensions.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_ttk\\test_extensions.py',
   'DATA'),
  ('tkinter\\test\\test_ttk\\test_style.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_ttk\\test_style.py',
   'DATA'),
  ('tkinter\\test\\test_ttk\\test_widgets.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\test_ttk\\test_widgets.py',
   'DATA'),
  ('tkinter\\test\\widget_tests.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\test\\widget_tests.py',
   'DATA'),
  ('tkinter\\tix.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\tix.py',
   'DATA'),
  ('tkinter\\ttk.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\tkinter\\ttk.py',
   'DATA'),
  ('utils\\__init__.py', 'D:\\mac\\视觉监控工具\\dist\\utils\\__init__.py', 'DATA'),
  ('utils\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\active_dialogue_processor06242.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\active_dialogue_processor06242.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\ai_chat.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\ai_chat.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\call_tool.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\call_tool.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\chat_summary_processor06151.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\chat_summary_processor06151.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\fixed_reply_processor.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\fixed_reply_processor.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\image_processor.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\image_processor.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\mouse_takeover_detector06191.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\mouse_takeover_detector06191.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\red_dot_detector.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\red_dot_detector.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\storage.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\storage.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\system_tools.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\system_tools.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\voice_read_detector.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\voice_read_detector.cpython-310.pyc',
   'DATA'),
  ('utils\\__pycache__\\win_send_file.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\__pycache__\\win_send_file.cpython-310.pyc',
   'DATA'),
  ('utils\\active_dialogue_processor06242.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\active_dialogue_processor06242.py',
   'DATA'),
  ('utils\\ai_chat.py', 'D:\\mac\\视觉监控工具\\dist\\utils\\ai_chat.py', 'DATA'),
  ('utils\\call_tool.py', 'D:\\mac\\视觉监控工具\\dist\\utils\\call_tool.py', 'DATA'),
  ('utils\\chat_summary_processor06151.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\chat_summary_processor06151.py',
   'DATA'),
  ('utils\\fixed_reply_processor.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\fixed_reply_processor.py',
   'DATA'),
  ('utils\\image_processor.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\image_processor.py',
   'DATA'),
  ('utils\\mac_send_file.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\mac_send_file.py',
   'DATA'),
  ('utils\\mouse_takeover_detector06191.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\mouse_takeover_detector06191.py',
   'DATA'),
  ('utils\\pyarmor_runtime_000000\\__init__.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\pyarmor_runtime_000000\\__init__.py',
   'DATA'),
  ('utils\\pyarmor_runtime_000000\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\mac\\视觉监控工具\\dist\\utils\\pyarmor_runtime_000000\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('utils\\red_dot_detector.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\red_dot_detector.py',
   'DATA'),
  ('utils\\sentence_similarity.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\sentence_similarity.py',
   'DATA'),
  ('utils\\storage.py', 'D:\\mac\\视觉监控工具\\dist\\utils\\storage.py', 'DATA'),
  ('utils\\system_tools.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\system_tools.py',
   'DATA'),
  ('utils\\voice_read_detector.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\voice_read_detector.py',
   'DATA'),
  ('utils\\win_send_file.py',
   'D:\\mac\\视觉监控工具\\dist\\utils\\win_send_file.py',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('astor\\VERSION',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\astor\\VERSION',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('docx\\templates\\default-docx-template\\customXml\\_rels\\item1.xml.rels',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\customXml\\_rels\\item1.xml.rels',
   'DATA'),
  ('docx\\templates\\default-settings.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-settings.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\customXml\\item1.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\customXml\\item1.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\docProps\\app.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\docProps\\app.xml',
   'DATA'),
  ('docx\\templates\\default-styles.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-styles.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\stylesWithEffects.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\word\\stylesWithEffects.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\customXml\\itemProps1.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\customXml\\itemProps1.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\docProps\\core.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\docProps\\core.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\styles.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\word\\styles.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\[Content_Types].xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\[Content_Types].xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\_rels\\.rels',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\_rels\\.rels',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\document.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\word\\document.xml',
   'DATA'),
  ('docx\\templates\\default-footer.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-footer.xml',
   'DATA'),
  ('docx\\py.typed',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\py.typed',
   'DATA'),
  ('docx\\templates\\default-header.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-header.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\webSettings.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\word\\webSettings.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\settings.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\word\\settings.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\theme\\theme1.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\word\\theme\\theme1.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\docProps\\thumbnail.jpeg',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\docProps\\thumbnail.jpeg',
   'DATA'),
  ('docx\\templates\\default-comments.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-comments.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\fontTable.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\word\\fontTable.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\numbering.xml',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\word\\numbering.xml',
   'DATA'),
  ('docx\\templates\\default.docx',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default.docx',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\_rels\\document.xml.rels',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\docx\\templates\\default-docx-template\\word\\_rels\\document.xml.rels',
   'DATA'),
  ('skimage\\morphology\\ball_decompositions.npy',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\morphology\\ball_decompositions.npy',
   'DATA'),
  ('skimage\\morphology\\disk_decompositions.npy',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\morphology\\disk_decompositions.npy',
   'DATA'),
  ('skimage\\filters\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\filters\\__init__.pyi',
   'DATA'),
  ('skimage\\transform\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\transform\\__init__.pyi',
   'DATA'),
  ('skimage\\feature\\orb_descriptor_positions.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\orb_descriptor_positions.txt',
   'DATA'),
  ('skimage\\feature\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\feature\\__init__.pyi',
   'DATA'),
  ('skimage\\color\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\color\\__init__.pyi',
   'DATA'),
  ('skimage\\draw\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\draw\\__init__.pyi',
   'DATA'),
  ('skimage\\measure\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\measure\\__init__.pyi',
   'DATA'),
  ('skimage\\exposure\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\exposure\\__init__.pyi',
   'DATA'),
  ('skimage\\io\\_plugins\\matplotlib_plugin.ini',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\io\\_plugins\\matplotlib_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\fits_plugin.ini',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\io\\_plugins\\fits_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\simpleitk_plugin.ini',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\io\\_plugins\\simpleitk_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\pil_plugin.ini',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\io\\_plugins\\pil_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\imageio_plugin.ini',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\io\\_plugins\\imageio_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\tifffile_plugin.ini',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\io\\_plugins\\tifffile_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\imread_plugin.ini',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\io\\_plugins\\imread_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\gdal_plugin.ini',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\io\\_plugins\\gdal_plugin.ini',
   'DATA'),
  ('skimage\\data\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\data\\__init__.pyi',
   'DATA'),
  ('skimage\\restoration\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\restoration\\__init__.pyi',
   'DATA'),
  ('skimage\\metrics\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\metrics\\__init__.pyi',
   'DATA'),
  ('skimage\\__init__.pyi',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\skimage\\__init__.pyi',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\console.tcl',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.7.tm',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tcl8\\8.5\\tcltest-2.5.7.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:/ProgramData/miniconda3/envs/weixin/Library/lib/tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('cv2\\config.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\config-3.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('markdown-3.8.dist-info\\INSTALLER',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\markdown-3.8.dist-info\\INSTALLER',
   'DATA'),
  ('markdown-3.8.dist-info\\REQUESTED',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\markdown-3.8.dist-info\\REQUESTED',
   'DATA'),
  ('markdown-3.8.dist-info\\licenses\\LICENSE.md',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\markdown-3.8.dist-info\\licenses\\LICENSE.md',
   'DATA'),
  ('markdown-3.8.dist-info\\top_level.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\markdown-3.8.dist-info\\top_level.txt',
   'DATA'),
  ('markdown-3.8.dist-info\\RECORD',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\markdown-3.8.dist-info\\RECORD',
   'DATA'),
  ('markdown-3.8.dist-info\\entry_points.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\markdown-3.8.dist-info\\entry_points.txt',
   'DATA'),
  ('markdown-3.8.dist-info\\WHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\markdown-3.8.dist-info\\WHEEL',
   'DATA'),
  ('markdown-3.8.dist-info\\METADATA',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\markdown-3.8.dist-info\\METADATA',
   'DATA'),
  ('setuptools-78.1.1-py3.10.egg-info\\SOURCES.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools-78.1.1-py3.10.egg-info\\SOURCES.txt',
   'DATA'),
  ('pydantic-2.11.7.dist-info\\INSTALLER',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\pydantic-2.11.7.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.5.dist-info\\INSTALLER',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy-2.2.5.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-78.1.1-py3.10.egg-info\\dependency_links.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools-78.1.1-py3.10.egg-info\\dependency_links.txt',
   'DATA'),
  ('setuptools-78.1.1-py3.10.egg-info\\entry_points.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools-78.1.1-py3.10.egg-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.5.dist-info\\REQUESTED',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy-2.2.5.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('albucore-0.0.24.dist-info\\RECORD',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albucore-0.0.24.dist-info\\RECORD',
   'DATA'),
  ('setuptools-78.1.1-py3.10.egg-info\\requires.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools-78.1.1-py3.10.egg-info\\requires.txt',
   'DATA'),
  ('setuptools-78.1.1-py3.10.egg-info\\PKG-INFO',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools-78.1.1-py3.10.egg-info\\PKG-INFO',
   'DATA'),
  ('albucore-0.0.24.dist-info\\METADATA',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albucore-0.0.24.dist-info\\METADATA',
   'DATA'),
  ('albumentations-2.0.8.dist-info\\METADATA',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albumentations-2.0.8.dist-info\\METADATA',
   'DATA'),
  ('albumentations-2.0.8.dist-info\\RECORD',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albumentations-2.0.8.dist-info\\RECORD',
   'DATA'),
  ('albumentations-2.0.8.dist-info\\WHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albumentations-2.0.8.dist-info\\WHEEL',
   'DATA'),
  ('albucore-0.0.24.dist-info\\top_level.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albucore-0.0.24.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-2.2.5.dist-info\\WHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy-2.2.5.dist-info\\WHEEL',
   'DATA'),
  ('albumentations-2.0.8.dist-info\\INSTALLER',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albumentations-2.0.8.dist-info\\INSTALLER',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\licenses\\LICENSE.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\urllib3-2.5.0.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\RECORD',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\urllib3-2.5.0.dist-info\\RECORD',
   'DATA'),
  ('albucore-0.0.24.dist-info\\INSTALLER',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albucore-0.0.24.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.5.dist-info\\DELVEWHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy-2.2.5.dist-info\\DELVEWHEEL',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\INSTALLER',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\urllib3-2.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('pydantic-2.11.7.dist-info\\WHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\pydantic-2.11.7.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-78.1.1-py3.10.egg-info\\top_level.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\setuptools-78.1.1-py3.10.egg-info\\top_level.txt',
   'DATA'),
  ('albucore-0.0.24.dist-info\\WHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albucore-0.0.24.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.5.dist-info\\LICENSE.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy-2.2.5.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('pydantic-2.11.7.dist-info\\RECORD',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\pydantic-2.11.7.dist-info\\RECORD',
   'DATA'),
  ('pydantic-2.11.7.dist-info\\licenses\\LICENSE',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\pydantic-2.11.7.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('numpy-2.2.5.dist-info\\RECORD',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy-2.2.5.dist-info\\RECORD',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\METADATA',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\urllib3-2.5.0.dist-info\\METADATA',
   'DATA'),
  ('albumentations-2.0.8.dist-info\\top_level.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albumentations-2.0.8.dist-info\\top_level.txt',
   'DATA'),
  ('pydantic-2.11.7.dist-info\\METADATA',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\pydantic-2.11.7.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.5.dist-info\\METADATA',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy-2.2.5.dist-info\\METADATA',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\WHEEL',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\urllib3-2.5.0.dist-info\\WHEEL',
   'DATA'),
  ('albucore-0.0.24.dist-info\\LICENSE',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albucore-0.0.24.dist-info\\LICENSE',
   'DATA'),
  ('albumentations-2.0.8.dist-info\\licenses\\LICENSE',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\albumentations-2.0.8.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('numpy-2.2.5.dist-info\\entry_points.txt',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\numpy-2.2.5.dist-info\\entry_points.txt',
   'DATA'),
  ('cv2\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'D:\\mac\\视觉监控工具\\dist\\build\\main\\base_library.zip',
   'DATA')],
 'python310.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
