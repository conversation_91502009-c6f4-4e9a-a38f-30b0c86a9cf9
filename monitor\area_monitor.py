import threading
import time
import os
import tempfile
import cv2
import pyautogui
import platform
import numpy as np
from PIL import ImageGrab
from utils.image_processor import capture_screen_area
from utils.red_dot_detector import get_red_area_xy
from pynput.mouse import <PERSON><PERSON>, Controller

class AreaMonitor:
    def __init__(self, app_controller):
        """
        初始化区域监控器
        :param app_controller: 应用控制器对象，用于与主程序交互
        """
        self.controller = app_controller
        self.monitoring = False
        self.preview_thread = None
        self.monitor_thread = None
        self.contact_thread = None
        self.preview_interval = 1.0
        self.thread_stop_timeout = 10.0  # 线程停止的最大等待时间(秒)
        # 添加线程锁，确保线程启停的原子性
        self.thread_lock = threading.Lock()

    def _interruptible_sleep(self, duration):
        """可中断的睡眠方法，能够快速响应停止信号"""
        sleep_step = 0.1  # 每次睡眠0.1秒
        elapsed = 0
        while elapsed < duration and self.monitoring:
            time.sleep(sleep_step)
            elapsed += sleep_step

    def start_monitoring(self):
        """开始监控所有区域"""
        with self.thread_lock:
            if not self.controller.selection_done:
                self.controller.log("请先选择区域后再开始监控")
                return False
            
            # 如果已经在监控，先确保停止现有监控
            if self.monitoring:
                self.controller.debug_log("检测到监控已在运行，先停止现有监控")
                self.stop_monitoring()
                # 给线程一些时间完全退出
                time.sleep(0.5)
            
            # 重置线程引用
            self.preview_thread = None
            self.monitor_thread = None
            self.contact_thread = None
            
            # 确保设置监控状态的原子性
            self.monitoring = True
            
            # 更新设置
            self.preview_interval = self.controller.ui.preview_interval_var.get()
            
            # 创建并启动预览线程
            self.preview_thread = threading.Thread(target=self.continuous_preview, name="Preview_Thread")
            self.preview_thread.daemon = True
            self.preview_thread.start()
            
            # 创建并启动聊天监控线程
            self.monitor_thread = threading.Thread(target=self.monitor_chat, name="Chat_Monitor_Thread")
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
            
            # 创建并启动联系人区域监控线程
            self.contact_thread = threading.Thread(target=self.monitor_contact_area, name="Contact_Monitor_Thread")
            self.contact_thread.daemon = True
            self.contact_thread.start()
            
            self.controller.debug_log(f"已创建并启动所有监控线程: {threading.active_count()} 个线程活跃")
            self.controller.is_exec_chat_area = False
            return True
    
    def stop_monitoring(self):
        """停止所有监控"""
        with self.thread_lock:
            if not self.monitoring:
                # 如果已经停止，直接返回
                return True
                
            # 设置停止标志
            self.monitoring = False
            self.controller.debug_log("设置监控停止标志，等待线程退出...")
            
            # 等待线程结束
            start_time = time.time()
            threads_to_check = [
                (self.preview_thread, "预览"),
                (self.monitor_thread, "聊天监控"), 
                (self.contact_thread, "联系人监控")
            ]
            
            # 设置最大等待时间
            max_wait_time = self.thread_stop_timeout
            
            for thread, name in threads_to_check:
                if thread and thread.is_alive():
                    thread_wait_start = time.time()
                    # 计算剩余的等待时间
                    remaining_time = max(0, max_wait_time - (thread_wait_start - start_time))
                    
                    # 等待线程结束，但不超过剩余的等待时间
                    thread.join(timeout=remaining_time)
                    
                    # 检查线程是否仍在运行
                    if thread.is_alive():
                        self.controller.log(f"警告: {name}线程未能在{remaining_time:.2f}秒内退出")
                    else:
                        self.controller.debug_log(f"{name}线程已正常退出")
            
            # 清除线程引用
            self.preview_thread = None
            self.monitor_thread = None
            self.contact_thread = None
            
            # 记录线程停止情况
            elapsed = time.time() - start_time
            active_threads = threading.active_count()
            self.controller.debug_log(f"监控线程停止操作完成，耗时{elapsed:.2f}秒，当前活跃线程数: {active_threads}")
            self.controller.is_exec_chat_area = False
            
            return True
    
    def continuous_preview(self):
        """连续更新预览的线程"""
        thread_id = threading.get_ident()
        self.controller.debug_log(f"预览线程启动: ID {thread_id}")

        try:
            while self.monitoring:
                try:
                    # 通过主控制器请求更新预览
                    self.controller.update_preview()
                    # 使用可中断的睡眠
                    self._interruptible_sleep(self.preview_interval)
                except Exception as e:
                    self.controller.log(f"预览更新异常: {str(e)}")
                    self._interruptible_sleep(1)

            self.controller.debug_log(f"预览线程正常退出: ID {thread_id}")
        except Exception as e:
            self.controller.log(f"预览线程异常退出: {str(e)}")
    
    def capture_contact_area(self):
        """截取联系人区域的图像"""
        x1, y1, x2, y2 = self.controller.contact_area
        if platform.system() == 'Darwin':
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        else:
            # Windows下需确保颜色空间一致
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
            # 将PIL图像转换为numpy数组，然后从RGB转为BGR(OpenCV默认格式)
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
    
    def capture_unread_bubble_area(self):
        """截取未读气泡区域的图像"""
        if not hasattr(self.controller, 'unread_bubble_area') or not self.controller.unread_bubble_area:
            return None
            
        x1, y1, x2, y2 = self.controller.unread_bubble_area
        if platform.system() == 'Darwin':
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        else:
            # Windows下需确保颜色空间一致
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
            # 将PIL图像转换为numpy数组，然后从RGB转为BGR(OpenCV默认格式)
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
    
    def monitor_contact_area(self):
        """监控联系人区域，检测红点并点击"""
        thread_id = threading.get_ident()
        self.controller.debug_log(f"联系人监控线程启动: ID {thread_id}")
        
        try:
            while self.monitoring:
                try:
                    # 如果正在回复，跳过红点检测
                    if self.controller.is_replying or self.controller.is_exec_chat_area:
                        # # 检查回复是否超时
                        # current_time = time.time()
                        # if current_time - self.controller.reply_start_time > self.controller.reply_timeout:
                        #     self.controller.log(f"回复超过{self.controller.reply_timeout}秒未完成，强制重置回复状态")
                        #     self.controller.is_replying = False
                        # else:
                        self.controller.debug_log("正在执行聊天区域作业，跳过红点检测")
                        self._interruptible_sleep(0.5)
                        continue
                    
                    img = self.capture_contact_area()

                    # 在debug模式下保存联系人区域截图
                    if self.controller.debug_mode:
                        debug_dir = "debug_screenshots"
                        os.makedirs(debug_dir, exist_ok=True)
                        timestamp = time.strftime("%Y%m%d_%H%M%S")
                        contact_img_path = os.path.join(debug_dir, f"contact_area_{timestamp}.png")
                        
                        # Ensure img is a valid NumPy array
                        if img is None:
                            self.controller.debug_log("Error: contact area img is None")
                        else:
                            if not isinstance(img, np.ndarray):
                                self.controller.debug_log(f"Converting contact img from {type(img)} to numpy array")
                                try:
                                    img = np.array(img)
                                except Exception as e:
                                    self.controller.debug_log(f"Cannot convert to numpy array: {str(e)}")
                                    
                            # Ensure proper image format (uint8)
                            if img.dtype != np.uint8:
                                self.controller.debug_log(f"Converting image from {img.dtype} to uint8")
                                img = img.astype(np.uint8)
                        
                        cv2.imwrite(contact_img_path, img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                        self.controller.debug_log(f"已保存联系人区域截图: {contact_img_path}")
                    
                    
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
                        cv2.imwrite(tmp.name, img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                        tmp_path = tmp.name
                    
                    # 读取用户设置的红点颜色和容差
                    r = self.controller.ui.red_r_var.get()
                    g = self.controller.ui.red_g_var.get()
                    b = self.controller.ui.red_b_var.get()
                    tol = self.controller.ui.red_tol_var.get()
                    
                    # 注意：在OpenCV中是BGR顺序，而不是RGB
                    lower_red_bgr = (max(0, b-tol), max(0, g-tol), max(0, r-tol))
                    upper_red_bgr = (min(255, b+tol), min(255, g+tol), min(255, r+tol))
                    
                    self.controller.debug_log(f"红点检测参数 - BGR下限: {lower_red_bgr}, BGR上限: {upper_red_bgr}")
                    
                    x, y = get_red_area_xy(tmp_path, lower_red_bgr, upper_red_bgr,self.controller.debug_mode)
                    
                    if x is not None and y is not None:
                        print(f"在联系人区域找到红点，坐标: ({x}, {y})")
                    else:
                        print("未在联系人区域找到红点")
                    
                    os.remove(tmp_path)
                    
                    # 如果联系人区域没有找到红点，则检查未读气泡区域
                    if x is None or y is None:
                        if hasattr(self.controller, 'unread_bubble_area') and self.controller.unread_bubble_area:
                            print("联系人区域未找到红点，检查未读气泡区域")
                            
                            # 捕获未读气泡区域图像
                            unread_img = self.capture_unread_bubble_area()
                            
                            if unread_img is not None:
                                # 在debug模式下保存未读气泡区域截图
                                if self.controller.debug_mode:
                                    debug_dir = "debug_screenshots"
                                    os.makedirs(debug_dir, exist_ok=True)
                                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                                    unread_img_path = os.path.join(debug_dir, f"unread_bubble_area_{timestamp}.png")
                                    
                                    # Ensure unread_img is a valid NumPy array
                                    if unread_img is None:
                                        self.controller.debug_log("Error: unread_img is None")
                                    else:
                                        if not isinstance(unread_img, np.ndarray):
                                            self.controller.debug_log(f"Converting unread_img from {type(unread_img)} to numpy array")
                                            try:
                                                unread_img = np.array(unread_img)
                                            except Exception as e:
                                                self.controller.debug_log(f"Cannot convert to numpy array: {str(e)}")
                                                
                                        # Ensure proper image format (uint8)
                                        if unread_img.dtype != np.uint8:
                                            self.controller.debug_log(f"Converting image from {unread_img.dtype} to uint8")
                                            unread_img = unread_img.astype(np.uint8)
                                    
                                    cv2.imwrite(unread_img_path, unread_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                                    self.controller.debug_log(f"已保存未读气泡区域截图: {unread_img_path}")
                                
                                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
                                    cv2.imwrite(tmp.name, unread_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                                    unread_tmp_path = tmp.name
                                
                                # 使用相同的红点检测参数
                                unread_x, unread_y = get_red_area_xy(unread_tmp_path, lower_red_bgr, upper_red_bgr, self.controller.debug_mode)
                                
                                if self.controller.debug_mode:
                                    if unread_x is not None and unread_y is not None:
                                        print(f"在未读气泡区域找到红点，坐标: ({unread_x}, {unread_y})")
                                    else:
                                        print("未在未读气泡区域找到红点")
                                
                                os.remove(unread_tmp_path)
                                
                                # 如果在未读气泡区域找到红点，则双击它
                                # 判断当前是否在回复状态
                                if self.controller.is_replying:
                                    self.controller.debug_log("正在回复状态，跳过未读气泡区域红点检测")
                                    self._interruptible_sleep(0.5)
                                    continue
                                
                                if unread_x is not None and unread_y is not None:
                                    x1, y1, x2, y2 = self.controller.unread_bubble_area
                                    click_x = x1 + unread_x
                                    click_y = y1 + unread_y
                                    self.controller.log(f"检测到未读气泡区红点，准备执行双击({click_x}, {click_y})")

                                    # 在双击未读气泡区域前，再次检测联系人区域是否有红点
                                    print("双击前再次检测联系人区域红点...")
                                    time.sleep(1)
                                    contact_img = self.capture_contact_area()

                                    if contact_img is not None:
                                        # 保存联系人区域截图用于检测
                                        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as contact_tmp:
                                            cv2.imwrite(contact_tmp.name, contact_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                                            contact_tmp_path = contact_tmp.name

                                        # 使用相同的红点检测参数
                                        contact_check_x, contact_check_y = get_red_area_xy(contact_tmp_path, lower_red_bgr, upper_red_bgr, self.controller.debug_mode)

                                        # 清理临时文件
                                        os.remove(contact_tmp_path)

                                        if contact_check_x is not None and contact_check_y is not None:
                                            print(f"双击前在联系人区域发现红点，放弃本次未读气泡区域点击")
                                            # 清理未读气泡区域临时文件
                                            os.remove(unread_tmp_path)
                                            return  # 直接返回，放弃本次点击
                                        else:
                                            print("联系人区域无红点，继续执行未读气泡区域双击")

                                    # 移动并双击红点
                                    if platform.system() == 'Darwin':
                                        pyautogui.moveTo(click_x, click_y)
                                        pyautogui.click()
                                        time.sleep(0.2)
                                        pyautogui.click()
                                        time.sleep(0.2)
                                        # 移动到聊天区中央
                                        pyautogui.moveTo(self.controller.chat_area[0] + self.controller.chat_area[2] / 2, self.controller.chat_area[1] + self.controller.chat_area[3] / 2)
                                    else:
                                        mouse = Controller()
                                        mouse.position = (click_x, click_y)
                                        mouse.click(Button.left)
                                        time.sleep(0.2)
                                        mouse.click(Button.left)
                                        time.sleep(0.2)
                                        mouse.click(Button.left)
                                        time.sleep(0.2)
                                        mouse.position = (self.controller.chat_area[0] + self.controller.chat_area[2] / 2, self.controller.chat_area[1] + self.controller.chat_area[3] / 2)
                                    
                                    # 等待一段时间让UI响应
                                    self._interruptible_sleep(1.5)
                
                    # 联系人区域找到了红点，进行处理
                    elif x is not None and y is not None:
                        # 判断当前是否在回复状态
                        if self.controller.is_replying:
                            self.controller.debug_log("正在回复状态，跳过联系人区红点检测")
                            self._interruptible_sleep(0.5)
                            continue
                        
                        x1, y1, x2, y2 = self.controller.contact_area
                        click_x = x1 + x
                        click_y = y1 + y
                        self.controller.log(f"检测到联系人区红点，点击({click_x}, {click_y})")
                        
                        # 【更改点1】暂时阻止聊天区域监控，直到联系人识别完成
                        self.controller.contact_switching = True
                        
                        # 保存当前会话的状态
                        if self.controller.current_contact:
                            self.controller.save_conversation(self.controller.current_contact, self.controller.conversation_history)
                            self.controller.debug_log(f"已保存当前会话'{self.controller.current_contact}'的状态")
                        
                        # 点击切换会话
                        if platform.system() == "Darwin":
                            pyautogui.moveTo(click_x, click_y)
                            pyautogui.click()
                        else:
                            mouse = Controller()
                            mouse.position = (click_x, click_y)
                            mouse.click(Button.left)
                        
                        # 等待会话切换完成
                        self._interruptible_sleep(1)
                        
                        # 【重要：先识别联系人名称，再处理聊天区域】
                        if hasattr(self.controller, 'contact_name_area') and self.controller.contact_name_area:
                            self.controller.log("点击红点后开始识别联系人名称...")
                            contact_name_identified = False
                            max_retries = 3  # 最多尝试3次识别
                            
                            for attempt in range(max_retries):
                                try:
                                    # 间隔一小段时间再尝试识别，给页面切换留出时间
                                    time.sleep(0.3 * (attempt + 1))
                                    
                                    # 捕获联系人姓名区域
                                    nx1, ny1, nx2, ny2 = self.controller.contact_name_area
                                    name_img = self.capture_area(nx1, ny1, nx2, ny2)
                                    
                                    # 在debug模式下保存联系人姓名区域截图
                                    if self.controller.debug_mode:
                                        debug_dir = "debug_screenshots"
                                        os.makedirs(debug_dir, exist_ok=True)
                                        timestamp = time.strftime("%Y%m%d_%H%M%S")
                                        name_img_path = os.path.join(debug_dir, f"contact_name_area_{timestamp}_attempt{attempt+1}.png")
                                        
                                        # Ensure name_img is a valid NumPy array
                                        if name_img is None:
                                            self.controller.debug_log("Error: name_img is None")
                                        else:
                                            if not isinstance(name_img, np.ndarray):
                                                self.controller.debug_log(f"Converting name_img from {type(name_img)} to numpy array")
                                                try:
                                                    name_img = np.array(name_img)
                                                except Exception as e:
                                                    self.controller.debug_log(f"Cannot convert to numpy array: {str(e)}")
                                                    
                                            # Ensure proper image format (uint8)
                                            if name_img.dtype != np.uint8:
                                                self.controller.debug_log(f"Converting image from {name_img.dtype} to uint8")
                                                name_img = name_img.astype(np.uint8)
                                        
                                        cv2.imwrite(name_img_path, name_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                                        self.controller.debug_log(f"已保存联系人姓名区域截图: {name_img_path}")
                                    
                                    if platform.system() == 'Darwin':
                                        # 使用OCR识别联系人姓名
                                        result = self.controller.message_extractor.ocr.ocr(name_img, cls=True)
                                        
                                        # 提取识别出的文本
                                        contact_name = ""
                                        if result and len(result) > 0:
                                            # 使用第一个识别结果作为联系人姓名
                                            for line in result:
                                                if isinstance(line, list) and len(line) > 0:
                                                    for item in line:
                                                        if len(item) >= 2 and item[1] is not None and len(item[1]) >= 2:
                                                            text = item[1][0].strip()
                                                            if text:
                                                                contact_name += text
                                    else:
                                        ocr_params = {'debug_mode': self.controller.debug_mode}
                                        contact_name = self.controller.message_extractor.extract_contact_name(name_img, ocr_params)
                                    
                                    # Windows平台特殊处理，如果OCR失败，尝试使用extract_contact_name方法
                                    if not contact_name and platform.system() == 'Windows':
                                        self.controller.debug_log(f"尝试 {attempt+1}/{max_retries}: 直接OCR失败，尝试使用extract_contact_name方法")
                                        try:
                                            # 使用专门的联系人提取方法
                                            ocr_params = {'debug_mode': self.controller.debug_mode}
                                            contact_name = self.controller.message_extractor.extract_contact_name(name_img, ocr_params)
                                        except Exception as e:
                                            self.controller.debug_log(f"extract_contact_name方法也失败: {str(e)}")
                                    
                                    if contact_name:
                                        self.controller.log(f"识别到联系人姓名: {contact_name}")
                                        # 切换到新的联系人
                                        switch_result, _ = self.controller.switch_contact(contact_name)
                                        if switch_result:
                                            contact_name_identified = True
                                            break  # 识别成功，跳出重试循环
                                        else:
                                            self.controller.log("切换联系人失败，将重试")
                                    else:
                                        self.controller.debug_log(f"尝试 {attempt+1}/{max_retries}: 未能识别联系人姓名，将重试")
                                except Exception as e:
                                    self.controller.debug_log(f"尝试 {attempt+1}/{max_retries} 识别联系人姓名时出错: {str(e)}")
                            
                            if not contact_name_identified:
                                self.controller.log("多次尝试后仍无法识别联系人名称，将使用'未知联系人'")
                                switch_result, _ = self.controller.switch_contact("未知联系人")
                                if switch_result:
                                    self.controller.debug_log("已切换到'未知联系人'")
                        else:
                            self.controller.log("未设置联系人姓名区域，无法识别联系人")
                            switch_result, _ = self.controller.switch_contact("未知联系人")
                            
                        # 重置消息监控状态
                        self.controller.last_messages = []
                        self.controller.last_message_count = 0
                        
                        # 标记联系人切换已完成，允许处理聊天区域
                        self.controller.contact_switch_triggered = True
                        self.controller.contact_switching = False
                        self.controller.log("联系人切换完成，可以开始处理聊天区域消息")
                        
                        # 等待一小段时间确保消息区域已经更新
                        self._interruptible_sleep(0.5)

                    self._interruptible_sleep(1)
                except Exception as e:
                    self.controller.log(f"联系人区监控异常: {str(e)}")
                    import traceback
                    self.controller.debug_log(traceback.format_exc())
                    # 出错时恢复正常状态
                    self.controller.contact_switching = False
                    self._interruptible_sleep(2)
            
            self.controller.debug_log(f"联系人监控线程正常退出: ID {thread_id}")
        except Exception as e:
            self.controller.log(f"联系人监控线程异常退出: {str(e)}")
    
    def monitor_chat(self):
        """监控聊天区域，识别消息并处理回复"""
        thread_id = threading.get_ident()
        self.controller.log(f"聊天监控线程启动: ID {thread_id}")
        cycle_count = 0
        last_process_time = 0  # 上次处理的时间
        min_process_interval = 0.5  # 最小处理间隔，防止过于频繁的OCR

        # 记录启动时的状态
        self.controller.debug_log(f"聊天监控线程启动时状态: monitoring={self.monitoring}, stop_requested={self.controller.stop_requested06141}, is_exec_chat_area={self.controller.is_exec_chat_area}")

        try:
            while self.monitoring:
                try:
                    # 防抖处理
                    current_time = time.time()
                    if current_time - last_process_time < min_process_interval:
                        self._interruptible_sleep(0.1)
                        continue
                        
                    cycle_count += 1
                    self.controller.debug_log(f"==== 监控周期 #{cycle_count} ====")
                    
                    # 调用控制器的处理方法
                    self.controller.process_chat_area()
                    
                    # 更新处理时间
                    last_process_time = time.time()
                    
                    # 间隔时间
                    interval = self.controller.ui.interval_var.get()
                    self.controller.debug_log(f"等待 {interval} 秒后进行下一次检测...")
                    self._interruptible_sleep(interval)
                    
                except Exception as e:
                    self.controller.log(f"监控异常: {str(e)}")
                    import traceback
                    self.controller.log(traceback.format_exc())
                    self._interruptible_sleep(2)  # 出错后暂停一下
                
            self.controller.debug_log(f"聊天监控线程正常退出: ID {thread_id}")
        except Exception as e:
            self.controller.log(f"聊天监控线程异常退出: {str(e)}")
    
    def capture_area(self, x1, y1, x2, y2):
        """捕获指定区域的截图"""
        if platform.system() == 'Darwin':
            screenshot = ImageGrab.grab(bbox=(x1, y1, x2, y2))
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        else:
            # Windows下需确保颜色空间一致
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
            # 将PIL图像转换为numpy数组，然后从RGB转为BGR(OpenCV默认格式)
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR) 