([('main.exe', 'D:\\mac\\视觉监控工具\\dist\\build\\main\\main.exe', 'EXECUTABLE'),
  ('python310.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\python310.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'D:\\mac\\视觉监控工具\\dist\\pyarmor_runtime_000000\\pyarmor_runtime.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'C:\\ProgramData\\miniconda3\\envs\\weixin\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\Library\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\ProgramData\\miniconda3\\envs\\weixin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'D:\\mac\\视觉监控工具\\dist\\build\\main\\base_library.zip',
   'DATA')],)
