import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import uuid
import platform


class FixedReplyUI:
    """固定回复UI界面"""

    def __init__(self, parent, callbacks=None):
        """
        初始化固定回复UI
        :param parent: 父窗口
        :param callbacks: 回调函数字典
        """
        self.parent = parent
        self.callbacks = callbacks or {}
        self.settings_file = "settings.json"

        # 当前选中的规则
        self.current_rule = None

        # 创建界面
        self.create_widgets()

        # 加载规则数据
        self.load_rules()

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建左右分栏
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # 左侧规则列表框架
        left_frame = ttk.LabelFrame(paned_window, text="规则列表", width=250)
        left_frame.pack_propagate(False)

        # 右侧规则详情框架
        right_frame = ttk.LabelFrame(paned_window, text="规则详情")

        paned_window.add(left_frame, weight=1)
        paned_window.add(right_frame, weight=3)

        # 创建左侧规则列表
        self.create_rules_list(left_frame)

        # 创建右侧规则详情
        self.create_rule_details(right_frame)

        # 创建底部操作按钮
        self.create_bottom_buttons(main_frame)

    def create_rules_list(self, parent):
        """创建规则列表"""
        # 规则列表
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview
        columns = ("name", "enabled")
        self.rules_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        self.rules_tree.heading("name", text="规则名称")
        self.rules_tree.heading("enabled", text="状态")

        # 设置列宽
        self.rules_tree.column("name", width=150)
        self.rules_tree.column("enabled", width=60)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.rules_tree.yview)
        self.rules_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.rules_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定选择事件
        self.rules_tree.bind("<<TreeviewSelect>>", self.on_rule_select)

    def create_rule_details(self, parent):
        """创建规则详情编辑区域"""
        # 创建滚动框架
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 规则基本信息
        basic_frame = ttk.LabelFrame(scrollable_frame, text="基本信息")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)

        # 规则名称
        ttk.Label(basic_frame, text="规则名称:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.rule_name_var = tk.StringVar()
        self.rule_name_entry = ttk.Entry(basic_frame, textvariable=self.rule_name_var, width=30)
        self.rule_name_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # 关键字
        ttk.Label(basic_frame, text="关键字:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.keyword_var = tk.StringVar()
        self.keyword_entry = ttk.Entry(basic_frame, textvariable=self.keyword_var, width=30)
        self.keyword_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # 匹配维度
        ttk.Label(basic_frame, text="匹配维度:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        match_frame = ttk.Frame(basic_frame)
        match_frame.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        self.match_user_var = tk.BooleanVar()
        self.match_ai_var = tk.BooleanVar()
        ttk.Checkbutton(match_frame, text="用户消息", variable=self.match_user_var).pack(side=tk.LEFT)
        ttk.Checkbutton(match_frame, text="AI回复", variable=self.match_ai_var).pack(side=tk.LEFT, padx=(10, 0))

        # 启用状态
        self.enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(basic_frame, text="启用规则", variable=self.enabled_var).grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # 执行动作
        actions_frame = ttk.LabelFrame(scrollable_frame, text="执行动作")
        actions_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 动作列表
        self.actions_frame = ttk.Frame(actions_frame)
        self.actions_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加动作按钮
        buttons_frame = ttk.Frame(actions_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(buttons_frame, text="添加文字", command=self.add_text_action).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="添加文件", command=self.add_file_action).pack(side=tk.LEFT, padx=5)

        # 动作列表容器
        self.actions_list_frame = ttk.Frame(self.actions_frame)
        self.actions_list_frame.pack(fill=tk.BOTH, expand=True)

        # 当前动作列表
        self.current_actions = []

        # 布局滚动框架
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_bottom_buttons(self, parent):
        """创建底部操作按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=10)

        ttk.Button(button_frame, text="新建规则", command=self.new_rule).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存规则", command=self.save_rule).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除规则", command=self.delete_rule).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="刷新列表", command=self.load_rules).pack(side=tk.LEFT, padx=5)
    
    def add_text_action(self):
        """添加文字动作"""
        action_frame = ttk.LabelFrame(self.actions_list_frame, text=f"文字动作 {len(self.current_actions) + 1}")
        action_frame.pack(fill=tk.X, padx=5, pady=5)

        # 文字内容
        ttk.Label(action_frame, text="文字内容:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        text_var = tk.StringVar()
        text_entry = ttk.Entry(action_frame, textvariable=text_var, width=40)
        text_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # 延时设置
        ttk.Label(action_frame, text="延时(秒):").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        delay_var = tk.DoubleVar(value=1.0)
        delay_entry = ttk.Entry(action_frame, textvariable=delay_var, width=10)
        delay_entry.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

        # 删除按钮
        delete_btn = ttk.Button(action_frame, text="删除",
                               command=lambda: self.remove_action(action_frame))
        delete_btn.grid(row=0, column=4, sticky=tk.W, padx=5, pady=5)

        # 保存动作信息
        action_data = {
            'type': 'text',
            'frame': action_frame,
            'text_var': text_var,
            'delay_var': delay_var
        }
        self.current_actions.append(action_data)

    def add_file_action(self):
        """添加文件动作"""
        action_frame = ttk.LabelFrame(self.actions_list_frame, text=f"文件动作 {len(self.current_actions) + 1}")
        action_frame.pack(fill=tk.X, padx=5, pady=5)

        # 文件路径
        ttk.Label(action_frame, text="文件路径:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        file_var = tk.StringVar()
        file_entry = ttk.Entry(action_frame, textvariable=file_var, width=30)
        file_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # 选择文件按钮
        browse_btn = ttk.Button(action_frame, text="浏览",
                               command=lambda: self.browse_file(file_var))
        browse_btn.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        # 延时设置
        ttk.Label(action_frame, text="延时(秒):").grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        delay_var = tk.DoubleVar(value=1.0)
        delay_entry = ttk.Entry(action_frame, textvariable=delay_var, width=10)
        delay_entry.grid(row=0, column=4, sticky=tk.W, padx=5, pady=5)

        # 删除按钮
        delete_btn = ttk.Button(action_frame, text="删除",
                               command=lambda: self.remove_action(action_frame))
        delete_btn.grid(row=0, column=5, sticky=tk.W, padx=5, pady=5)

        # 保存动作信息
        action_data = {
            'type': 'file',
            'frame': action_frame,
            'file_var': file_var,
            'delay_var': delay_var
        }
        self.current_actions.append(action_data)

    def remove_action(self, action_frame):
        """删除动作"""
        # 从当前动作列表中移除
        self.current_actions = [action for action in self.current_actions if action['frame'] != action_frame]
        # 销毁框架
        action_frame.destroy()
        # 重新编号
        self.renumber_actions()

    def renumber_actions(self):
        """重新编号动作"""
        for i, action in enumerate(self.current_actions):
            action_type = "文字动作" if action['type'] == 'text' else "文件动作"
            action['frame'].configure(text=f"{action_type} {i + 1}")

    def browse_file(self, file_var):
        """浏览选择文件"""
        file_path = filedialog.askopenfilename(
            title="选择文件",
            filetypes=[("所有文件", "*.*")]
        )
        if file_path:
            file_var.set(file_path)

    def load_rules(self):
        """加载规则列表"""
        # 清空现有列表
        for item in self.rules_tree.get_children():
            self.rules_tree.delete(item)

        # 从settings.json加载规则
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    rules = settings.get('fixed_reply_rules', [])

                    for rule in rules:
                        status = "启用" if rule.get('enabled', True) else "禁用"
                        self.rules_tree.insert("", tk.END, values=(rule.get('name', ''), status), tags=(rule.get('id', ''),))

            from utils.fixed_reply_processor import reload_fixed_reply_rules
            reload_fixed_reply_rules()
        except Exception as e:
            messagebox.showerror("错误", f"加载规则失败: {str(e)}")

    def on_rule_select(self, event):
        """规则选择事件"""
        selection = self.rules_tree.selection()
        if selection:
            item = selection[0]
            rule_id = self.rules_tree.item(item, "tags")[0]
            self.load_rule_details(rule_id)

    def load_rule_details(self, rule_id):
        """加载规则详情"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    rules = settings.get('fixed_reply_rules', [])

                    for rule in rules:
                        if rule.get('id') == rule_id:
                            self.current_rule = rule
                            self.display_rule_details(rule)
                            break
        except Exception as e:
            messagebox.showerror("错误", f"加载规则详情失败: {str(e)}")

    def display_rule_details(self, rule):
        """显示规则详情"""
        # 清空当前动作
        self.clear_actions()

        # 填充基本信息
        self.rule_name_var.set(rule.get('name', ''))
        self.keyword_var.set(rule.get('keyword', ''))

        # 匹配维度
        match_dimensions = rule.get('match_dimensions', [])
        self.match_user_var.set('user_message' in match_dimensions)
        self.match_ai_var.set('ai_reply' in match_dimensions)

        # 启用状态
        self.enabled_var.set(rule.get('enabled', True))

        # 加载动作
        actions = rule.get('actions', [])
        for action in actions:
            if action['type'] == 'text':
                self.add_text_action()
                self.current_actions[-1]['text_var'].set(action.get('content', ''))
                self.current_actions[-1]['delay_var'].set(action.get('delay', 1.0))
            elif action['type'] == 'file':
                self.add_file_action()
                self.current_actions[-1]['file_var'].set(action.get('file_path', ''))
                self.current_actions[-1]['delay_var'].set(action.get('delay', 1.0))

    def clear_actions(self):
        """清空当前动作"""
        for action in self.current_actions:
            action['frame'].destroy()
        self.current_actions = []

    def new_rule(self):
        """新建规则"""
        self.current_rule = None
        self.clear_actions()

        # 清空表单
        self.rule_name_var.set("")
        self.keyword_var.set("")
        self.match_user_var.set(True)
        self.match_ai_var.set(False)
        self.enabled_var.set(True)

    def save_rule(self):
        """保存规则"""
        # 验证输入
        rule_name = self.rule_name_var.get().strip()
        keyword = self.keyword_var.get().strip()

        if not rule_name:
            messagebox.showerror("错误", "请输入规则名称")
            return

        if not keyword:
            messagebox.showerror("错误", "请输入关键字")
            return

        if not self.match_user_var.get() and not self.match_ai_var.get():
            messagebox.showerror("错误", "请至少选择一个匹配维度")
            return

        # 构建规则数据
        rule_data = {
            'id': self.current_rule['id'] if self.current_rule else str(uuid.uuid4()),
            'name': self.rule_name_var.get().strip(),
            'keyword': self.keyword_var.get().strip(),
            'match_dimensions': [],
            'actions': [],
            'enabled': self.enabled_var.get()
        }

        # 匹配维度
        if self.match_user_var.get():
            rule_data['match_dimensions'].append('user_message')
        if self.match_ai_var.get():
            rule_data['match_dimensions'].append('ai_reply')

        # 动作
        for action in self.current_actions:
            if action['type'] == 'text':
                content = action['text_var'].get().strip()
                if content:
                    rule_data['actions'].append({
                        'type': 'text',
                        'content': content,
                        'delay': action['delay_var'].get()
                    })
            elif action['type'] == 'file':
                file_path = action['file_var'].get().strip()
                if file_path:
                    rule_data['actions'].append({
                        'type': 'file',
                        'file_path': file_path,
                        'delay': action['delay_var'].get()
                    })

        if not rule_data['actions']:
            messagebox.showerror("错误", "请至少添加一个执行动作")
            return

        # 保存到settings.json
        try:
            settings = {}
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

            if 'fixed_reply_rules' not in settings:
                settings['fixed_reply_rules'] = []

            # 更新或添加规则
            rules = settings['fixed_reply_rules']
            if self.current_rule:
                # 更新现有规则
                for i, rule in enumerate(rules):
                    if rule.get('id') == self.current_rule['id']:
                        rules[i] = rule_data
                        break
            else:
                # 添加新规则
                rules.append(rule_data)

            # 保存文件
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("成功", "规则保存成功")
            self.current_rule = rule_data
            self.load_rules()

        except Exception as e:
            messagebox.showerror("错误", f"保存规则失败: {str(e)}")

    def delete_rule(self):
        """删除规则"""
        if not self.current_rule:
            messagebox.showwarning("警告", "请先选择要删除的规则")
            return

        if messagebox.askyesno("确认", f"确定要删除规则 '{self.current_rule['name']}' 吗？"):
            try:
                if os.path.exists(self.settings_file):
                    with open(self.settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)

                    rules = settings.get('fixed_reply_rules', [])
                    rules = [rule for rule in rules if rule.get('id') != self.current_rule['id']]
                    settings['fixed_reply_rules'] = rules

                    with open(self.settings_file, 'w', encoding='utf-8') as f:
                        json.dump(settings, f, ensure_ascii=False, indent=2)

                    messagebox.showinfo("成功", "规则删除成功")
                    self.new_rule()
                    self.load_rules()

            except Exception as e:
                messagebox.showerror("错误", f"删除规则失败: {str(e)}")
